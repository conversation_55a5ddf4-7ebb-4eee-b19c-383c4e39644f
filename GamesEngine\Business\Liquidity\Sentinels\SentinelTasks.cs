﻿using GamesEngine.Business.Liquidity.Containers;
using GamesEngine.Business.Liquidity.Sentinels.Inbound;
using GamesEngine.Business.Liquidity.Sentinels.Outbound;
using GamesEngine.Business.Liquidity.Transactions;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using static GamesEngine.Business.Liquidity.Containers.Dispenser;
using static GamesEngine.Business.Liquidity.Containers.Tanker;
using static GamesEngine.Business.Liquidity.PaymentEngineDocks;


namespace GamesEngine.Business.Liquidity.Sentinels
{
    internal class SentinelTasks
    {
        internal static int DELAY_SECONDS_PER_CONFIRMATION = 3;

        private readonly ConcurrentDictionary<object, SentinelTask> boundTasks = new();

        internal SentinelTasks()
        {
        }

        internal IEnumerable<SentinelTask> Tasks => boundTasks.Values;

        internal SentinelTask FindTask(object objectKey)
        {
            if (objectKey == null) throw new ArgumentNullException(nameof(objectKey));

            switch (objectKey)
            {
                case Tanker tanker:
                    SentinelTask existingTask = boundTasks.Values.FirstOrDefault(x => x is TankerInboundTask _t && _t.Tanker.Id == tanker.Id);
                    if (existingTask != null) return existingTask as TankerInboundTask;
                    break;
                case Dispenser dispenser:
                    SentinelTask existingDispenserTask = boundTasks.Values.FirstOrDefault(x => x is ConfirmationsOutboundTask _d && _d.Dispenser.Id == dispenser.Id);
                    if (existingDispenserTask != null) return existingDispenserTask as ConfirmationsOutboundTask;
                    break;
                default:
                    if (boundTasks.TryGetValue(objectKey, out var inboundTask)) return inboundTask;
                    break;
            }
            throw new GameEngineException($"No inbound task found for invoice ID: {objectKey}.");
        }

        internal bool HasTask(object objectKey)
        {
            if (objectKey == null) throw new ArgumentNullException(nameof(objectKey));

            switch(objectKey)
            {
                case Tanker tanker:
                    SentinelTask existingTask = boundTasks.Values.FirstOrDefault(x => x is TankerInboundTask _t && _t.Tanker.Id == tanker.Id);
                    if (existingTask != null) return true;
                    break;
                case Dispenser dispenser:
                    SentinelTask existingDispenserTask = boundTasks.Values.FirstOrDefault(x => x is ConfirmationsOutboundTask _d && _d.Dispenser.Id == dispenser.Id);
                    if (existingDispenserTask != null) return true;
                    break;
                default:
                    return boundTasks.ContainsKey(objectKey);
            }
            return false;
        }

        internal void AddTask(object taskObjectKey, SentinelTask task)
        {
            if (taskObjectKey == null) throw new ArgumentNullException(nameof(taskObjectKey));
            if (task == null) throw new ArgumentNullException(nameof(task));

            if (!boundTasks.TryAdd(taskObjectKey, task))
            {
                throw new GameEngineException($"Task with key {taskObjectKey} already exists.");
            }
        }

        internal ConfirmationsInboundTask AddConfirmationsWatcher(Deposit deposit, int totalConfirmations, PaymentEngineDock engineDock)
        {
            if (deposit == null) throw new ArgumentNullException(nameof(deposit));
            if (totalConfirmations < 0) throw new ArgumentNullException(nameof(totalConfirmations));
            if (engineDock == null) throw new ArgumentNullException(nameof(engineDock));

            if (!boundTasks.TryGetValue(deposit, out SentinelTask confirmsInboundTask))
            {
                confirmsInboundTask = new ConfirmationsInboundTask(this, deposit, totalConfirmations, engineDock);
                boundTasks.TryAdd(deposit, confirmsInboundTask);
            }
            return confirmsInboundTask as ConfirmationsInboundTask;
        }

        internal void Detach(SentinelTask inoutboundTask)
        {
            if (inoutboundTask is ConfirmationsInboundTask confirmationsInbound)
            {
                boundTasks.TryRemove(confirmationsInbound.Deposit, out _);
            }
            else
            {
                boundTasks.TryRemove(inoutboundTask, out _);
            }
        }

        internal TankerInboundTask AwaitForTanker(TankerSealed tanker)
        {
            if (tanker == null) throw new ArgumentNullException(nameof(tanker));

            SentinelTask existingTask = boundTasks.Values.FirstOrDefault(x => x is TankerInboundTask _t && _t.Tanker.Id == tanker.Id);
            if (existingTask != null)
            {
                return existingTask as TankerInboundTask;
            }

            var newTankerInboundTask = new TankerInboundTask(tanker);
            boundTasks.TryAdd(newTankerInboundTask, newTankerInboundTask);
            return newTankerInboundTask;
        }

        internal ConfirmationsOutboundTask AwaitWithdrawalConfirmations(bool itIsThePresent, DateTime now, DispenserReady dispenserReady)
        {
            if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now));
            if (dispenserReady == null) throw new ArgumentNullException(nameof(dispenserReady));

            var confimationsOutboundTask = new ConfirmationsOutboundTask(now, this, dispenserReady);

            if (itIsThePresent) confimationsOutboundTask.StartTask(now);

            boundTasks.TryAdd(confimationsOutboundTask, confimationsOutboundTask);
            return confimationsOutboundTask;
        }
    }
}
