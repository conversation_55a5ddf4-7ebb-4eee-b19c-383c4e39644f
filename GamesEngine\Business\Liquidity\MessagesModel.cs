﻿using GamesEngine.Business.Liquidity.Containers;
using GamesEngine.MessageQueuing;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GamesEngine.Business.Liquidity
{
    public class DraftDepositMessage : TypedMessage
    {
        public int DepositId { get; internal set; }
        public string Kind { get; internal set; }
        public string Address { get; internal set; }
        public string TransactionId { get; internal set; }
        public decimal Amount { get; internal set; }
        public DateTime DraftDate { get; internal set; }
        public decimal Rate { get; internal set; }
        public int JarVersion { get; internal set; }
        public string Account { get; internal set; }
        public int DomainId { get; internal set; }
        public string AffiliateId { get; internal set; }

        public DraftDepositMessage(int depositId, string kind, string address, string transactionId, decimal amount, DateTime createdAt, int jarVersion, string account, int domainId, decimal rate, string affiliateId)
            : base((char)LiquidityMessageType.DraftDeposit)
        {
            if (depositId <= 0) throw new ArgumentOutOfRangeException(nameof(depositId));
            if (string.IsNullOrWhiteSpace(kind)) throw new ArgumentNullException(nameof(kind));
            if (string.IsNullOrWhiteSpace(address)) throw new ArgumentNullException(nameof(address));
            if (string.IsNullOrWhiteSpace(transactionId)) throw new ArgumentNullException(nameof(transactionId));
            if (amount <= 0) throw new ArgumentOutOfRangeException(nameof(amount));
            if (jarVersion <= 0) throw new ArgumentOutOfRangeException(nameof(jarVersion));
            if (string.IsNullOrWhiteSpace(account)) throw new ArgumentNullException(nameof(account));
            if (domainId <= 0) throw new ArgumentOutOfRangeException(nameof(domainId));
            if (createdAt == DateTime.MinValue) throw new ArgumentOutOfRangeException(nameof(createdAt));
            if (rate <= 0) throw new ArgumentOutOfRangeException(nameof(rate));
            if (string.IsNullOrWhiteSpace(affiliateId)) throw new ArgumentNullException(nameof(affiliateId));

            DepositId = depositId;
            Kind = kind;
            Address = address;
            TransactionId = transactionId;
            Amount = amount;
            JarVersion = jarVersion;
            Account = account;
            DomainId = domainId;
            DraftDate = createdAt;
            Rate = rate;
            AffiliateId = affiliateId;
        }

        public DraftDepositMessage(string serializedMessage) : base(serializedMessage) { }

        protected override void Deserialize(string[] serializedMessage, out int fieldOrder)
        {
            if (serializedMessage.Length <= 0) throw new GameEngineException("Invalid format to make a deposit");

            base.Deserialize(serializedMessage, out fieldOrder);
            DepositId = int.Parse(serializedMessage[fieldOrder++]);
            Kind = serializedMessage[fieldOrder++];
            Address = serializedMessage[fieldOrder++];
            TransactionId = serializedMessage[fieldOrder++];
            Amount = decimal.Parse(serializedMessage[fieldOrder++]);
            Rate = decimal.Parse(serializedMessage[fieldOrder++]);
            JarVersion = int.Parse(serializedMessage[fieldOrder++]);
            Account = serializedMessage[fieldOrder++];
            DomainId = int.Parse(serializedMessage[fieldOrder++]);
            AffiliateId = serializedMessage[fieldOrder++];

            int year = int.Parse(serializedMessage[fieldOrder++]);
            int month = int.Parse(serializedMessage[fieldOrder++]);
            int day = int.Parse(serializedMessage[fieldOrder++]);
            int hour = int.Parse(serializedMessage[fieldOrder++]);
            int minute = int.Parse(serializedMessage[fieldOrder++]);
            int second = int.Parse(serializedMessage[fieldOrder++]);
            DraftDate = new DateTime(year, month, day, hour, minute, second);
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(DepositId).
            AddProperty(Kind).
            AddProperty(Address).
            AddProperty(TransactionId).
            AddProperty(Amount).
            AddProperty(Rate).
            AddProperty(JarVersion).
            AddProperty(Account).
            AddProperty(DomainId).
            AddProperty(AffiliateId).
            AddProperty(DraftDate);
        }
    }

    public class ConfirmedDepositMessage : TypedMessage
    {
        public int DepositId { get; internal set; }
        public DateTime ConfirmedDate { get; internal set; }

        public ConfirmedDepositMessage(int depositId, DateTime createdAt) : base((char)LiquidityMessageType.ConfirmedDeposit)
        {
            DepositId = depositId;
            ConfirmedDate = createdAt;
        }

        public ConfirmedDepositMessage(string serializedMessage) : base(serializedMessage) { }

        protected override void Deserialize(string[] serializedMessage, out int fieldOrder)
        {
            if (serializedMessage.Length <= 0) throw new GameEngineException("Invalid format to make a deposit");
            base.Deserialize(serializedMessage, out fieldOrder);
            DepositId = int.Parse(serializedMessage[fieldOrder++]);

            int year = int.Parse(serializedMessage[fieldOrder++]);
            int month = int.Parse(serializedMessage[fieldOrder++]);
            int day = int.Parse(serializedMessage[fieldOrder++]);
            int hour = int.Parse(serializedMessage[fieldOrder++]);
            int minute = int.Parse(serializedMessage[fieldOrder++]);
            int second = int.Parse(serializedMessage[fieldOrder++]);
            ConfirmedDate = new DateTime(year, month, day, hour, minute, second);

        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(DepositId).
            AddProperty(ConfirmedDate);
        }
    }

    public class CreatedJarMessage : TypedMessage
    {
        public long Version { get; internal set; }
        public long PreviousVersion { get; internal set; }
        public string Kind { get; internal set; }
        public string Description { get; internal set; }
        public DateTime CreatedAt { get; internal set; }

        public CreatedJarMessage(long version, long previousVersion, string kind, string description, DateTime createdAt) : base((char)LiquidityMessageType.JarCreated)
        {
            Version = version;
            PreviousVersion = previousVersion;
            Kind = kind;
            Description = description;
            CreatedAt = createdAt;
        }

        public CreatedJarMessage(string serializedMessage) : base(serializedMessage) { }

        protected override void Deserialize(string[] serializedMessage, out int fieldOrder)
        {
            if (serializedMessage.Length <= 0) throw new GameEngineException("Invalid format for CreatedJarMessage");
            base.Deserialize(serializedMessage, out fieldOrder);
            Version = long.Parse(serializedMessage[fieldOrder++]);
            PreviousVersion = long.Parse(serializedMessage[fieldOrder++]);
            Kind = serializedMessage[fieldOrder++];
            Description = serializedMessage[fieldOrder++];

            int year = int.Parse(serializedMessage[fieldOrder++]);
            int month = int.Parse(serializedMessage[fieldOrder++]);
            int day = int.Parse(serializedMessage[fieldOrder++]);
            int hour = int.Parse(serializedMessage[fieldOrder++]);
            int minute = int.Parse(serializedMessage[fieldOrder++]);
            int second = int.Parse(serializedMessage[fieldOrder++]);
            CreatedAt = new DateTime(year, month, day, hour, minute, second);
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
                AddProperty(Version).
                AddProperty(PreviousVersion).
                AddProperty(Kind).
                AddProperty(Description).
                AddProperty(CreatedAt);
        }
    }

    public class TankDiscardedMessage : LiquidityEventMessage
    {
        public int TankId { get; internal set; }
        public string Kind { get; internal set; }
        public string Name { get; internal set; }
        public string Description { get; internal set; }
        public int Version { get; internal set; }
        public DateTime CreatedAt { get; internal set; }
        public IEnumerable<int> DepositIds { get; internal set; }
        public int Parent { get; internal set; }
        internal TankDiscardedMessage(int tankId, string kind, string name, string description, int version, int parentId, DateTime createdAt, IEnumerable<int> depositIds) : base(LiquidityMessageType.TankDiscarded)
        {
            TankId = tankId;
            Kind = kind;
            Name = name;
            Description = description;
            Version = version;
            Parent = parentId;
            CreatedAt = createdAt;
            DepositIds = depositIds ?? Enumerable.Empty<int>();
        }
        public TankDiscardedMessage(string message) : base(message)
        {
        }
        protected override void Deserialize(string[] serializedMessage, out int fieldOrder)
        {
            if (serializedMessage.Length <= 0) throw new GameEngineException("Invalid format to make a deposit");
            base.Deserialize(serializedMessage, out fieldOrder);
            TankId = int.Parse(serializedMessage[fieldOrder++]);
            Kind = serializedMessage[fieldOrder++];
            Name = serializedMessage[fieldOrder++];
            Description = serializedMessage[fieldOrder++];
            Version = int.Parse(serializedMessage[fieldOrder++]);
            int year = int.Parse(serializedMessage[fieldOrder++]);
            int month = int.Parse(serializedMessage[fieldOrder++]);
            int day = int.Parse(serializedMessage[fieldOrder++]);
            int hour = int.Parse(serializedMessage[fieldOrder++]);
            int minute = int.Parse(serializedMessage[fieldOrder++]);
            int second = int.Parse(serializedMessage[fieldOrder++]);
            CreatedAt = new DateTime(year, month, day, hour, minute, second);
            DepositIds = Enumerable.Empty<int>();
            for (int i = fieldOrder; i < serializedMessage.Length; i++)
            {
                DepositIds = DepositIds.Append(int.Parse(serializedMessage[i]));
            }
        }
        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(TankId).
            AddProperty(Kind).
            AddProperty(Name).
            AddProperty(Description).
            AddProperty(Version).
            AddProperty(CreatedAt);
            foreach (var depositId in DepositIds)
            {
                AddProperty(depositId);
            }
        }
    }

    public class TankArchivedMessage : LiquidityEventMessage
    {
        public int TankId { get; internal set; }
        public string Kind { get; internal set; }
        public string Name { get; internal set; }
        public string Description { get; internal set; }
        public int Version { get; internal set; }
        public DateTime CreatedAt { get; internal set; }
        public IEnumerable<int> DepositIds { get; internal set; }
        public int Parent { get; internal set; }
        internal TankArchivedMessage(int tankId, string kind, string name, string description, int version, int parentId, DateTime createdAt, IEnumerable<int> depositIds) : base(LiquidityMessageType.TankArchived)
        {
            TankId = tankId;
            Kind = kind;
            Name = name;
            Description = description;
            Version = version;
            Parent = parentId;
            CreatedAt = createdAt;
            DepositIds = depositIds ?? Enumerable.Empty<int>();
        }
        public TankArchivedMessage(string message) : base(message)
        {
        }
        protected override void Deserialize(string[] serializedMessage, out int fieldOrder)
        {
            if (serializedMessage.Length <= 0) throw new GameEngineException("Invalid format to make a deposit");
            base.Deserialize(serializedMessage, out fieldOrder);
            TankId = int.Parse(serializedMessage[fieldOrder++]);
            Kind = serializedMessage[fieldOrder++];
            Name = serializedMessage[fieldOrder++];
            Description = serializedMessage[fieldOrder++];
            Version = int.Parse(serializedMessage[fieldOrder++]);
            int year = int.Parse(serializedMessage[fieldOrder++]);
            int month = int.Parse(serializedMessage[fieldOrder++]);
            int day = int.Parse(serializedMessage[fieldOrder++]);
            int hour = int.Parse(serializedMessage[fieldOrder++]);
            int minute = int.Parse(serializedMessage[fieldOrder++]);
            int second = int.Parse(serializedMessage[fieldOrder++]);
            CreatedAt = new DateTime(year, month, day, hour, minute, second);
            DepositIds = Enumerable.Empty<int>();
            for (int i = fieldOrder; i < serializedMessage.Length; i++)
            {
                DepositIds = DepositIds.Append(int.Parse(serializedMessage[i]));
            }
        }
        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(TankId).
            AddProperty(Kind).
            AddProperty(Name).
            AddProperty(Description).
            AddProperty(Version).
            AddProperty(CreatedAt);
            foreach (var depositId in DepositIds)
            {
                AddProperty(depositId);
            }
        }
    }

    public class TankerArchivedMessage : LiquidityEventMessage
    {
        public int TankerId { get; internal set; }
        public string Kind { get; internal set; }
        public string Name { get; internal set; }
        public string Description { get; internal set; }
        public DateTime CreatedAt { get; internal set; }
        public int Version { get; internal set; }
        public IEnumerable<int> TankIds { get; internal set; }
        internal TankerArchivedMessage(int tankerId, string kind, string name, string description, DateTime createdAt, int version, IEnumerable<int> tanksIds) : base(LiquidityMessageType.TankerArchived)
        {
            TankerId = tankerId;
            Kind = kind;
            Name = name;
            Description = description;
            CreatedAt = createdAt;
            Version = version;
            TankIds = tanksIds ?? Enumerable.Empty<int>();
        }
        public TankerArchivedMessage(string message) : base(message)
        {
        }
        protected override void Deserialize(string[] serializedMessage, out int fieldOrder)
        {
            if (serializedMessage.Length <= 0) throw new GameEngineException("Invalid format to make a deposit");
            base.Deserialize(serializedMessage, out fieldOrder);
            TankerId = int.Parse(serializedMessage[fieldOrder++]);
            Kind = serializedMessage[fieldOrder++];
            Name = serializedMessage[fieldOrder++];
            Description = serializedMessage[fieldOrder++];
            int year = int.Parse(serializedMessage[fieldOrder++]);
            int month = int.Parse(serializedMessage[fieldOrder++]);
            int day = int.Parse(serializedMessage[fieldOrder++]);
            int hour = int.Parse(serializedMessage[fieldOrder++]);
            int minute = int.Parse(serializedMessage[fieldOrder++]);
            int second = int.Parse(serializedMessage[fieldOrder++]);
            CreatedAt = new DateTime(year, month, day, hour, minute, second);
            Version = int.Parse(serializedMessage[fieldOrder++]);
            TankIds = Enumerable.Empty<int>();
            for (int i = fieldOrder; i < serializedMessage.Length; i++)
            {
                TankIds = TankIds.Append(int.Parse(serializedMessage[i]));
            }
        }
        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(TankerId).
            AddProperty(Kind).
            AddProperty(Name).
            AddProperty(string.IsNullOrWhiteSpace(Description) ? "EMPTY": Description).
            AddProperty(CreatedAt).
            AddProperty(Version);
            foreach (var tankId in TankIds)
            {
                AddProperty(tankId);
            }
        }
    }

    public class CreatedLiquidMessage : TypedMessage
    {
        public string Kind { get; internal set; }
        public DateTime CreatedAt { get; internal set; }

        public CreatedLiquidMessage(string kind, DateTime createdAt) : base((char)LiquidityMessageType.LiquidCreated)
        {
            Kind = kind;
            CreatedAt = createdAt;
        }

        public CreatedLiquidMessage(string serializedMessage) : base(serializedMessage) { }

        protected override void Deserialize(string[] serializedMessage, out int fieldOrder)
        {
            if (serializedMessage.Length <= 0) throw new GameEngineException("Invalid format for CreatedLiquidMessage");
            base.Deserialize(serializedMessage, out fieldOrder);
            Kind = serializedMessage[fieldOrder++];

            int year = int.Parse(serializedMessage[fieldOrder++]);
            int month = int.Parse(serializedMessage[fieldOrder++]);
            int day = int.Parse(serializedMessage[fieldOrder++]);
            int hour = int.Parse(serializedMessage[fieldOrder++]);
            int minute = int.Parse(serializedMessage[fieldOrder++]);
            int second = int.Parse(serializedMessage[fieldOrder++]);
            CreatedAt = new DateTime(year, month, day, hour, minute, second);
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(Kind).
            AddProperty(CreatedAt);
        }
    }

    public class CreatedDispenserMessage : TypedMessage
    {
        public int Id { get; internal set; }
        public string Kind { get; internal set; }
        public decimal Amount { get; internal set; }
        public DateTime StartDate { get; internal set; }
        public int Version { get; internal set; }
        public IEnumerable<int> WithdrawalIds { get; internal set; }

        public CreatedDispenserMessage(int id, string kind, decimal amount, DateTime dispenserStartDate, int version, List<int> withdrawalIds) : base((char)LiquidityMessageType.DispenserCreated)
        {
            Id = id;
            Kind = kind;
            Amount = amount;
            StartDate = dispenserStartDate;
            Version = version;
            WithdrawalIds = withdrawalIds ?? Enumerable.Empty<int>();
        }

        public CreatedDispenserMessage(string serializedMessage) : base(serializedMessage) { }

        protected override void Deserialize(string[] serializedMessage, out int fieldOrder)
        {
            if (serializedMessage.Length <= 0) throw new GameEngineException("Invalid format for CreatedDispenserMessage");
            base.Deserialize(serializedMessage, out fieldOrder);
            Id = int.Parse(serializedMessage[fieldOrder++]);
            Kind = serializedMessage[fieldOrder++];
            Amount = decimal.Parse(serializedMessage[fieldOrder++]);

            int year = int.Parse(serializedMessage[fieldOrder++]);
            int month = int.Parse(serializedMessage[fieldOrder++]);
            int day = int.Parse(serializedMessage[fieldOrder++]);
            int hour = int.Parse(serializedMessage[fieldOrder++]);
            int minute = int.Parse(serializedMessage[fieldOrder++]);
            int second = int.Parse(serializedMessage[fieldOrder++]);
            StartDate = new DateTime(year, month, day, hour, minute, second);
            Version = int.Parse(serializedMessage[fieldOrder++]);
            WithdrawalIds = Enumerable.Empty<int>();
            for (int i = fieldOrder; i < serializedMessage.Length; i++)
            {
                WithdrawalIds = WithdrawalIds.Append(int.Parse(serializedMessage[i]));
            }
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(Id).
            AddProperty(Kind).
            AddProperty(Amount).
            AddProperty(StartDate).
            AddProperty(Version);
            foreach (var withdrawalId in WithdrawalIds)
            {
                AddProperty(withdrawalId);
            }
        }
    }

    public class CreatedBottleMessage : TypedMessage
    {
        public int Id { get; internal set; }
        public string Name { get; internal set; }
        public string Kind { get; internal set; }
        public DateTime CreatedAt { get; internal set; }

        public CreatedBottleMessage(int id, string name, string kind, DateTime createdAt) : base((char)LiquidityMessageType.BottleCreated)
        {
            Id = id;
            Name = name;
            Kind = kind;
            CreatedAt = createdAt;
        }

        public CreatedBottleMessage(string serializedMessage) : base(serializedMessage) { }

        protected override void Deserialize(string[] serializedMessage, out int fieldOrder)
        {
            if (serializedMessage.Length <= 0) throw new GameEngineException("Invalid format for CreatedBottleMessage");
            base.Deserialize(serializedMessage, out fieldOrder);
            Id = int.Parse(serializedMessage[fieldOrder++]);
            Name = serializedMessage[fieldOrder++];
            Kind = serializedMessage[fieldOrder++];
            int year = int.Parse(serializedMessage[fieldOrder++]);
            int month = int.Parse(serializedMessage[fieldOrder++]);
            int day = int.Parse(serializedMessage[fieldOrder++]);
            int hour = int.Parse(serializedMessage[fieldOrder++]);
            int minute = int.Parse(serializedMessage[fieldOrder++]);
            int second = int.Parse(serializedMessage[fieldOrder++]);
            CreatedAt = new DateTime(year, month, day, hour, minute, second);
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(Id).
            AddProperty(Name).
            AddProperty(Kind).
            AddProperty(CreatedAt);
        }

    }
    public class InvoicePaidMessage : TypedMessage
    {
        public string Kind { get; internal set; }
        public string InvoiceId { get; internal set; }
        public string DestinationAddress { get; internal set; }
        public decimal PaidAmount { get; internal set; }
        public DateTime PaidAt { get; internal set; }
        public string TransactionId { get; internal set; }
        public decimal FeePaid { get; internal set; }

        public InvoicePaidMessage(string invoiceId, string kind, string destinationAddress, decimal paidAmount, string transactionId, decimal feePaid, DateTime paidAt)
            : base((char)LiquidityMessageType.InvoicePaid)
        {
            if (string.IsNullOrWhiteSpace(invoiceId)) throw new ArgumentNullException(nameof(invoiceId));
            if (string.IsNullOrWhiteSpace(kind)) throw new ArgumentNullException(nameof(kind));
            if (string.IsNullOrWhiteSpace(destinationAddress)) throw new ArgumentNullException(nameof(destinationAddress));
            if (paidAmount <= 0) throw new ArgumentOutOfRangeException(nameof(paidAmount));
            if (paidAt == DateTime.MinValue) throw new ArgumentOutOfRangeException(nameof(paidAt));
            if (string.IsNullOrWhiteSpace(transactionId)) throw new ArgumentNullException(nameof(transactionId));
            if (feePaid < 0) throw new ArgumentOutOfRangeException(nameof(feePaid));

            InvoiceId = invoiceId;
            Kind = kind;
            DestinationAddress = destinationAddress;
            PaidAmount = paidAmount;
            TransactionId = transactionId;
            FeePaid = feePaid;
            PaidAt = paidAt;
        }

        public InvoicePaidMessage(string serializedMessage) : base(serializedMessage) { }

        protected override void Deserialize(string[] serializedMessage, out int fieldOrder)
        {
            if (serializedMessage.Length <= 0) throw new GameEngineException("Invalid format for InvoicePaidMessage");
            base.Deserialize(serializedMessage, out fieldOrder);
            InvoiceId = serializedMessage[fieldOrder++];
            Kind = serializedMessage[fieldOrder++];
            DestinationAddress = serializedMessage[fieldOrder++];
            PaidAmount = decimal.Parse(serializedMessage[fieldOrder++]);
            TransactionId = serializedMessage[fieldOrder++];
            FeePaid = decimal.Parse(serializedMessage[fieldOrder++]);

            int year = int.Parse(serializedMessage[fieldOrder++]);
            int month = int.Parse(serializedMessage[fieldOrder++]);
            int day = int.Parse(serializedMessage[fieldOrder++]);
            int hour = int.Parse(serializedMessage[fieldOrder++]);
            int minute = int.Parse(serializedMessage[fieldOrder++]);
            int second = int.Parse(serializedMessage[fieldOrder++]);
            PaidAt = new DateTime(year, month, day, hour, minute, second);
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(InvoiceId).
            AddProperty(Kind).
            AddProperty(DestinationAddress).
            AddProperty(PaidAmount).
            AddProperty(TransactionId).
            AddProperty(FeePaid).
            AddProperty(PaidAt);
        }
    }

    public class CreatedWithdrawalMessage : TypedMessage
    {
        public string Kind { get; internal set; }
        public int Id { get; internal set; }
        public decimal Amount { get; internal set; }
        public DateTime CreatedDate { get; internal set; }
        public string Destination { get; internal set; }
        public string AtAddress { get; internal set; }
        public string ExternalReference { get; internal set; }
        public int DispenserId { get; internal set; }
        public int DomainId { get; internal set; }
        public decimal Rate { get; internal set; }

        public CreatedWithdrawalMessage(string kind, int id, decimal amount, DateTime createdDate, string destination, string atAddress, string externalReference, int dispenserId, int domainId,decimal rate)
            : base((char)LiquidityMessageType.WithdrawalCreated)
        {
            if (string.IsNullOrWhiteSpace(kind)) throw new ArgumentNullException(nameof(kind));
            if (id <= 0) throw new ArgumentOutOfRangeException(nameof(id));
            if (amount <= 0) throw new ArgumentOutOfRangeException(nameof(amount));
            if (createdDate == DateTime.MinValue) throw new ArgumentOutOfRangeException(nameof(createdDate));
            if (string.IsNullOrWhiteSpace(destination)) throw new ArgumentNullException(nameof(destination));
            if (string.IsNullOrWhiteSpace(atAddress)) throw new ArgumentNullException(nameof(atAddress));
            if (string.IsNullOrWhiteSpace(externalReference)) throw new ArgumentNullException(nameof(externalReference));
            if (dispenserId <= 0) throw new ArgumentOutOfRangeException(nameof(dispenserId));
            if (domainId <= 0) throw new ArgumentOutOfRangeException(nameof(domainId));
            if (rate <= 0) throw new ArgumentOutOfRangeException(nameof(rate));

            Kind = kind;
            Id = id;
            Amount = amount;
            CreatedDate = createdDate;
            Destination = destination;
            AtAddress = atAddress;
            ExternalReference = externalReference;
            DispenserId = dispenserId;
            DomainId = domainId;
            Rate = rate;
        }

        public CreatedWithdrawalMessage(string serializedMessage) : base(serializedMessage) { }

        protected override void Deserialize(string[] serializedMessage, out int fieldOrder)
        {
            if (serializedMessage.Length <= 0) throw new GameEngineException("Invalid format for CreatedWithdrawalMessage");
            base.Deserialize(serializedMessage, out fieldOrder);
            Kind = serializedMessage[fieldOrder++];
            Id = int.Parse(serializedMessage[fieldOrder++]);
            Amount = decimal.Parse(serializedMessage[fieldOrder++]);

            int year = int.Parse(serializedMessage[fieldOrder++]);
            int month = int.Parse(serializedMessage[fieldOrder++]);
            int day = int.Parse(serializedMessage[fieldOrder++]);
            int hour = int.Parse(serializedMessage[fieldOrder++]);
            int minute = int.Parse(serializedMessage[fieldOrder++]);
            int second = int.Parse(serializedMessage[fieldOrder++]);
            CreatedDate = new DateTime(year, month, day, hour, minute, second);

            Destination = serializedMessage[fieldOrder++];
            AtAddress = serializedMessage[fieldOrder++];
            ExternalReference = serializedMessage[fieldOrder++];
            DispenserId = int.Parse(serializedMessage[fieldOrder++]);
            DomainId = int.Parse(serializedMessage[fieldOrder++]);
            Rate = decimal.Parse(serializedMessage[fieldOrder++]);
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(Kind)
            .AddProperty(Id)
            .AddProperty(Amount)
            .AddProperty(CreatedDate)
            .AddProperty(Destination)
            .AddProperty(AtAddress)
            .AddProperty(ExternalReference)
            .AddProperty(DispenserId)
            .AddProperty(DomainId)
            .AddProperty(Rate);
        }
    }

    public class CanceledDepositMessage : TypedMessage
    {
        public int DepositId { get; internal set; }
        public DateTime CanceledDate { get; internal set; }

        public CanceledDepositMessage(int depositId, DateTime canceledDate) : base((char)LiquidityMessageType.DepositCanceled)
        {
            if (depositId <= 0) throw new ArgumentNullException(nameof(depositId));
            if (canceledDate == DateTime.MinValue) throw new ArgumentOutOfRangeException(nameof(canceledDate));

            DepositId = depositId;
            CanceledDate = canceledDate;
        }
        public CanceledDepositMessage(string serializedMessage) : base(serializedMessage) { }

        protected override void Deserialize(string[] serializedMessage, out int fieldOrder)
        {
            if (serializedMessage.Length <= 0) throw new GameEngineException("Invalid format for CanceledDepositMessage");
            base.Deserialize(serializedMessage, out fieldOrder);
            DepositId = int.Parse(serializedMessage[fieldOrder++]);
            int year = int.Parse(serializedMessage[fieldOrder++]);
            int month = int.Parse(serializedMessage[fieldOrder++]);
            int day = int.Parse(serializedMessage[fieldOrder++]);
            int hour = int.Parse(serializedMessage[fieldOrder++]);
            int minute = int.Parse(serializedMessage[fieldOrder++]);
            int second = int.Parse(serializedMessage[fieldOrder++]);
            CanceledDate = new DateTime(year, month, day, hour, minute, second);
        }
        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(DepositId).
            AddProperty(CanceledDate);
        }
    }

    public enum LiquidityMessageType
    {
        // Sentinel
        SentinelConfirmDeposit = '0',

        // LIQUIDITY EVENTS
        DraftDeposit = 'A',
        ConfirmedDeposit = 'B',
        LiquidCreated = 'C',
        JarCreated = 'D',
        TankCreated = 'E',
        TankMerged = 'F',
        TankerCreated = 'G',
        DispenserCreated = 'H',
        BottleCreated = 'I',
        InvoicePaid = 'J',
        WithdrawalCreated = 'K',

        // Tansactions
        WithdrawalCanceled = 'X',
        DepositCanceled = 'Y',

        // CONTAINER EVENTS
        ExchangeRateChange = 'L',
        TankerDispatched = 'M',
        DispatchedDispenser = 'N',
        DispenserWithdrawalsCommitted = 'R',
        AddedWithdrawalsCommitDispenser = 'S',

        // BI
        TankDiscarded = 'O',
        TankArchived = 'P',
        TankerArchived = 'Q',
    }
}
