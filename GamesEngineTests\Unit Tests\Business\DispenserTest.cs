﻿using Elastic.Clients.Elasticsearch;
using GamesEngine.Business.Liquidity;
using GamesEngine.Business.Liquidity.Containers;
using GamesEngine.Domains;
using GamesEngineTests.Unit_Tests.Games.Lotto;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using static GamesEngine.Business.Liquidity.Containers.Dispenser;
using static GamesEngine.Finance.PaymentChannels;

namespace GamesEngineTests.Unit_Tests.Business
{
    [TestClass]
    public class DispenserTest
    {
        [TestMethod]
        public async Task TestWithdrawBottleAsync()
        {
            CurrenciesTest.AddCurrencies();

            DateTime now = DateTime.Now;
            bool itIsThePresent = false;
            Liquid liquidBTC = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "BTC");
            liquidBTC.Source.AddXpub(new Xpub("dasfsdfdsfsd"));

            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);

            liquidBTC.Source.Liquid.ParentFlow.AddPaymentDock(domain, "ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag");


            var deposit1 = liquidBTC.Source.CreateDraftDeposit(itIsThePresent, now, liquidBTC.Source.NextDepositId(), "invoice123", 1, 22, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(liquidBTC.Source.Amount == 0);

            liquidBTC.Source.ConfirmDeposit(itIsThePresent, now, deposit1);
            Assert.IsTrue(liquidBTC.Source.Amount == 1);

            int tankId = liquidBTC.Source.NextTankId();
            int nextJarVerion = liquidBTC.Source.NextJarVersion();

            var tank = liquidBTC.Source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank1", "Fondos para navidad", nextJarVerion, liquidBTC.Source.Jar.ConfirmedDeposits());
            Assert.IsTrue(liquidBTC.Source.Amount == 1);
            Assert.IsTrue(tank != null);
            Assert.IsTrue(tank.Id == tankId);
            Assert.IsTrue(tank.Name == "Tank1");
            Assert.IsTrue(tank.Version == liquidBTC.Source.Jar.PreviousLegacyJar.Version);
            Assert.IsTrue(tank.Amount == 1);

            int tankerId = liquidBTC.Source.NextTankerId();
            var tanker = liquidBTC.Source.CreateTanker(itIsThePresent, now, tankerId, "Tanker 1", "Description", new List<int> { tank.Id });
            Assert.IsTrue(tanker != null);
            Assert.IsTrue(tanker.Id == tankerId);
            Assert.IsTrue(tanker.Name == "Tanker 1");
            Assert.IsTrue(tanker.Description == "Description");
            Assert.IsTrue(tanker.Amount == 1);

            var tankerSeal = tanker.Sealed();
            Assert.IsTrue(liquidBTC.Source.Amount == 1);
            Assert.IsTrue(tankerSeal != null);
            Assert.IsTrue(tankerSeal.Id == tanker.Id);
            Assert.IsTrue(tankerSeal.Name == tanker.Name);
            Assert.IsTrue(tankerSeal.Description == tanker.Description);
            Assert.IsTrue(tankerSeal.Amount == tanker.Amount);

            int withdrawalId = liquidBTC.Outlet.NextWithdrawalId();
            int bottleId = liquidBTC.Outlet.NextBottleId();
            decimal amount = 10;
            string destionation = "tb1q4...";
            string reference = "abc123";
            string externalReference = "external123";
            string atAddress = "cr1234";
            var withdrawal = liquidBTC.Outlet.CreateWithdrawal(itIsThePresent, now, withdrawalId, 1, amount, destionation, atAddress, domain, 1, externalReference, rate: 110000, receivedAmount: 10, receivedCurrency: "USD");
            Assert.IsTrue(liquidBTC.Outlet.NextWithdrawalId() == 2);

            int dispenserId = liquidBTC.Outlet.NextDispenserId();
            var dispenser = liquidBTC.Outlet.CreateDispenser(itIsThePresent, now, dispenserId, "MiDispenser", "description", now.AddDays(10));
            Assert.IsTrue(liquidBTC.Outlet.NextDispenserId() == 2);

            liquidBTC.Outlet.DispenserInbox.MoveWithdrawal(itIsThePresent, dispenserId, withdrawal);
            Assert.IsTrue(liquidBTC.Outlet.DispenserInbox.Withdrawals.Count() == 0, "Withdrawals should be empty in the inbox after moving to dispenser");
            Assert.IsTrue(dispenser.Withdrawals.Count() == 1, "Dispenser should have one withdrawal after moving from inbox");
        }

        [TestMethod]
        public async Task TestDispenserCreationAsync()
        {
            CurrenciesTest.AddCurrencies();

            DateTime now = DateTime.Now;
            bool itIsThePresent = false;
            Liquid liquidBTC = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "BTC");
            liquidBTC.Source.AddXpub(new Xpub("dasfsdfdsfsd"));

            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);
            liquidBTC.Source.Liquid.ParentFlow.AddPaymentDock(domain, "ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag");

            var deposit1 = liquidBTC.Source.CreateDraftDeposit(itIsThePresent, now, liquidBTC.Source.NextDepositId(), "invoice123", 1, 22, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(liquidBTC.Source.Amount == 0);

            liquidBTC.Source.ConfirmDeposit(itIsThePresent, now, deposit1);
            Assert.IsTrue(liquidBTC.Source.Amount == 1);

            int tankId = liquidBTC.Source.NextTankId();
            int nextJarVerion = liquidBTC.Source.NextJarVersion();

            var tank = liquidBTC.Source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank1", "Fondos para navidad", nextJarVerion, liquidBTC.Source.Jar.ConfirmedDeposits());
            Assert.IsTrue(liquidBTC.Source.Amount == 1);
            Assert.IsTrue(tank != null);
            Assert.IsTrue(tank.Id == tankId);
            Assert.IsTrue(tank.Name == "Tank1");
            Assert.IsTrue(tank.Version == 1);
            Assert.IsTrue(tank.Amount == 1);

            int tankerId = liquidBTC.Source.NextTankerId();
            var tanker = liquidBTC.Source.CreateTanker(itIsThePresent, now, tankerId, "Tanker 1", "Description", new List<int> { tank.Id });
            Assert.IsTrue(tanker != null);
            Assert.IsTrue(tanker.Id == tankerId);
            Assert.IsTrue(tanker.Name == "Tanker 1");
            Assert.IsTrue(tanker.Description == "Description");
            Assert.IsTrue(tanker.Amount == 1);

            var tankerSeal = tanker.Sealed();
            Assert.IsTrue(liquidBTC.Source.Amount == 1);
            Assert.IsTrue(tankerSeal != null);
            Assert.IsTrue(tankerSeal.Id == tanker.Id);
            Assert.IsTrue(tankerSeal.Name == tanker.Name);
            Assert.IsTrue(tankerSeal.Description == tanker.Description);
            Assert.IsTrue(tankerSeal.Amount == tanker.Amount);

            int dispenserId = liquidBTC.Outlet.NextDispenserId();
            bool existDispenser = liquidBTC.Outlet.ExistDispenser(dispenserId);
            Assert.IsFalse(existDispenser, "Dispenser should not exist before creation");

            var dispenser = liquidBTC.Outlet.CreateDispenser(itIsThePresent, now, dispenserId, "MiDispenser", "description", now.AddDays(10));
            existDispenser = liquidBTC.Outlet.ExistDispenser(dispenserId);
            Assert.IsTrue(existDispenser, "Dispenser should exist after creation");

            int withdrawalId = liquidBTC.Outlet.NextWithdrawalId();
            int bottleId = liquidBTC.Outlet.NextBottleId();
            decimal amount = 10;
            string address = "tb1q4..."; // Replace with a valid address for testing purposes
            //var bottle = liquidBTC.Outlet.Dispense(dispenser.Id, bottleId, "Pizza Time", withdrawalId, amount, address, now);

            //Assert.IsTrue(dispenser != null);
            //Assert.IsTrue(dispenser.Id == dispenserId);
            //Assert.IsTrue(dispenser.Address == "fc8a29223e51761f00edd77b54133939483180247990092bcdf8a3874e7f4e90");
            //Assert.IsTrue(dispenser.Amount == 100);
            //Assert.IsTrue(dispenser.StartDate == now.AddDays(1));
            //Assert.IsTrue(dispenser.Enabled == true);
            //Assert.IsTrue(dispenser.Liquid == liquidBTC);
            //Assert.IsTrue(dispenser.Type == typeof(DispenserReady).Name);
            //Assert.IsTrue(dispenser.Kind == "BTC");

            //int bottleId = liquidBTC.Outlet.NextBottleId();
            //int withdrawalId = liquidBTC.Outlet.NextWithdrawalId();
            //var withdrawal = dispenser.Dispense(bottleId, withdrawalId, "1MiDispenser", 10, now.AddDays(1));

            //BottlePending bottlePending = liquidBTC.Outlet.CreateBottle(itIsThePresent, now, bottleId, "my first bottle", dispenser);
            //bottlePending.Process(itIsThePresent, now);

            //Bottle actualBottle = liquidBTC.Outlet.FindBottle(bottleId);
            //Assert.IsTrue(actualBottle is BottleProcessing);

            //await Task.Delay(TimeSpan.FromSeconds(15));

            //actualBottle = liquidBTC.Outlet.FindBottle(bottleId);
            //Assert.IsTrue(actualBottle is BottleCompleted);
        }

        [TestMethod]
        public async Task TestDispenserMoveWithdrawalFromDispenserToAnotherDispenserAsync()
        {
            CurrenciesTest.AddCurrencies();

            string txid = "h1h77has7as7";
            int domainId = 1;

            DateTime now = DateTime.Now;
            LiquidFlow.ClearInstance();
            bool itIsThePresent = false;
            Liquid liquidBTC = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "BTC");
            liquidBTC.Source.AddXpub(new Xpub("dasfsdfdsfsd"));
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);

            liquidBTC.Source.Liquid.ParentFlow.AddPaymentDock(domain, "ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag");

            var deposit1 = liquidBTC.Source.CreateDraftDeposit(itIsThePresent, now, liquidBTC.Source.NextDepositId(), "invoice123", 1, 22, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(liquidBTC.Source.Amount == 0);

            liquidBTC.Source.ConfirmDeposit(itIsThePresent, now, deposit1);
            Assert.IsTrue(liquidBTC.Source.Amount == 1);

            int tankId = liquidBTC.Source.NextTankId();
            int nextJarVerion = liquidBTC.Source.NextJarVersion();

            var tank = liquidBTC.Source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank1", "Fondos para navidad", nextJarVerion, liquidBTC.Source.Jar.ConfirmedDeposits());
            Assert.IsTrue(liquidBTC.Source.Amount == 1);
            Assert.IsTrue(tank != null);
            Assert.IsTrue(tank.Id == tankId);
            Assert.IsTrue(tank.Name == "Tank1");
            Assert.IsTrue(tank.Version == 1);
            Assert.IsTrue(tank.Amount == 1);

            int tankerId = liquidBTC.Source.NextTankerId();
            var tanker = liquidBTC.Source.CreateTanker(itIsThePresent, now, tankerId, "Tanker 1", "Description", new List<int> { tank.Id });
            Assert.IsTrue(tanker != null);
            Assert.IsTrue(tanker.Id == tankerId);
            Assert.IsTrue(tanker.Name == "Tanker 1");
            Assert.IsTrue(tanker.Description == "Description");
            Assert.IsTrue(tanker.Amount == 1);

            var tankerSeal = tanker.Sealed();
            Assert.IsTrue(liquidBTC.Source.Amount == 1);
            Assert.IsTrue(tankerSeal != null);
            Assert.IsTrue(tankerSeal.Id == tanker.Id);
            Assert.IsTrue(tankerSeal.Name == tanker.Name);
            Assert.IsTrue(tankerSeal.Description == tanker.Description);
            Assert.IsTrue(tankerSeal.Amount == tanker.Amount);

            int dispenserId = liquidBTC.Outlet.NextDispenserId();
            bool existDispenser = liquidBTC.Outlet.ExistDispenser(dispenserId);
            Assert.IsFalse(existDispenser, "Dispenser should not exist before creation");

            var dispenser = liquidBTC.Outlet.CreateDispenser(itIsThePresent, now, dispenserId, "MiDispenser", "description", now.AddDays(10));
            existDispenser = liquidBTC.Outlet.ExistDispenser(dispenserId);
            Assert.IsTrue(existDispenser, "Dispenser should exist after creation");

            int withdrawalId = liquidBTC.Outlet.NextWithdrawalId();
            int bottleId = liquidBTC.Outlet.NextBottleId();
            decimal amount = 10;
            string address = "tb1q4..."; // Replace with a valid address for testing purposes

            dispenserId = liquidBTC.Outlet.NextDispenserId();
            existDispenser = liquidBTC.Outlet.ExistDispenser(dispenserId);
            Assert.IsFalse(existDispenser, "Dispenser should not exist before creation");

            DispenserReady dispenser2 = liquidBTC.Outlet.CreateDispenser(itIsThePresent, now, dispenserId, "Dispenser 2", "description", now.AddDays(10));
            existDispenser = liquidBTC.Outlet.ExistDispenser(dispenserId);
            Assert.IsTrue(existDispenser, "Dispenser should exist after creation");

            string destionation = "tb1q4...";
            string reference = "abc123";
            string externalReference = "external123";
            string atAddress = "cr1234";
            var withdrawal = liquidBTC.Outlet.CreateWithdrawal(itIsThePresent, now, withdrawalId, 1, amount, destionation, atAddress, domain, 1, externalReference, rate: 110000, receivedAmount: 10, receivedCurrency: "USD");

            liquidBTC.Outlet.DispenserInbox.MoveWithdrawal(itIsThePresent, dispenser.Id, withdrawal);
            Assert.IsTrue(liquidBTC.Outlet.DispenserInbox.Withdrawals.Count() == 0, "Withdrawals should be empty in the inbox after moving to dispenser");

            Assert.IsTrue(dispenser.Withdrawals.Count() == 1, "Dispenser should have one withdrawal after moving from inbox");
            var existWithdrawal = liquidBTC.Outlet.ExistWithdrawalInDispenser(dispenser.Id, withdrawal.Id);
            Assert.IsTrue(existWithdrawal, "Withdrawal should exist in the first dispenser after moving from inbox");
            dispenser.MoveWithdrawal(itIsThePresent, dispenser2.Id, withdrawal);

            dispenser2.MoveWithdrawal(itIsThePresent, liquidBTC.Outlet.DispenserInbox.Id, withdrawal);
            dispenser2 = liquidBTC.Outlet.FindDispenser(dispenser2.Id) as DispenserReady;
            Assert.IsTrue(dispenser.Withdrawals.Count() == 0, "Withdrawals should be empty in the first dispenser after moving to another dispenser");
            Assert.IsTrue(dispenser2.Withdrawals.Count() == 0, "The second dispenser should have one withdrawal after moving from the first dispenser");
            try
            {
                existWithdrawal = liquidBTC.Outlet.ExistWithdrawalInDispenser(dispenser.Id, withdrawal.Id);
                Assert.IsFalse(existWithdrawal, "Withdrawal should not exist in the first dispenser after moving to another dispenser");
            }
            catch (Exception ex)
            {
                Assert.Fail($"An error occurred while checking withdrawal existence: {ex.Message}");
            }

            try
            {
               var exist = dispenser2.HasWithdrawal(withdrawal.Id);
                Assert.IsTrue(exist, "Withdrawal should exist in the second dispenser after moving from the first dispenser");
            }
            catch (Exception ex)
            {
                Assert.Fail($"An error occurred while checking withdrawal existence: {ex.Message}");
            }
        }

        [TestMethod]
        public async Task TestDispenserMoveDispersAsync()
        {
            CurrenciesTest.AddCurrencies();

            string txid = "h1h77has7as7";
            int domainId = 1;

            DateTime now = DateTime.Now;
            bool itIsThePresent = false;
            Liquid liquidBTC = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "BTC");
            liquidBTC.Source.AddXpub(new Xpub("dasfsdfdsfsd"));

            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);

            int dispenserId = liquidBTC.Outlet.NextDispenserId();
            bool existDispenser = liquidBTC.Outlet.ExistDispenser(dispenserId);
            Assert.IsFalse(existDispenser, "Dispenser should not exist before creation");

            int withdrawalId = liquidBTC.Outlet.NextWithdrawalId();
            string destionation = "tb1q4...";
            string reference = "abc123";
            string externalReference = "external123";
            string atAddress = "cr1234";
            decimal amount = 1;
            var withdrawal = liquidBTC.Outlet.CreateWithdrawal(itIsThePresent, now, withdrawalId, 1, amount, destionation, atAddress, domain, 1, externalReference, rate: 110000, receivedAmount: 10, receivedCurrency: "USD");

        }

        [TestMethod]
        public void TestDispenserInbox_WithdrawalsBy_ReturnsCorrectWithdrawals()
        {
            // Arrange
            CurrenciesTest.AddCurrencies();
            LiquidFlow.ClearInstance();
            DateTime now = new DateTime(2023, 10, 26, 12, 0, 0);
            bool itIsThePresent = false;
            Liquid liquidBTC = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "BTC");
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);
            var dispenserInbox = liquidBTC.Outlet.DispenserInbox;

            // Create withdrawals on different dates
            var withdrawal_Day1 = dispenserInbox.CreateWithdrawal(now.AddDays(-2), 1, 1, 10, "dest1", "addr1", domain, 1, "ext1", rate: 110000, receivedAmount: 10, receivedCurrency: "USD"); // Oct 24
            var withdrawal_Day2_Start = dispenserInbox.CreateWithdrawal(now.AddDays(-1).Date, 2, 2, 20, "dest2", "addr2", domain, 1, "ext2", rate: 110000, receivedAmount: 10, receivedCurrency: "USD"); // Oct 25 00:00:00
            var withdrawal_Day2_End = dispenserInbox.CreateWithdrawal(now.AddDays(-1).Date.AddDays(1).AddSeconds(-1), 3, 3, 30, "dest3", "addr3", domain, 1, "ext3", rate: 110000, receivedAmount: 10, receivedCurrency: "USD"); // Oct 25 23:59:59
            var withdrawal_Day3_Start = dispenserInbox.CreateWithdrawal(now.Date, 4, 4, 40, "dest4", "addr4", domain, 1, "ext4", rate: 110000, receivedAmount: 10, receivedCurrency: "USD"); // Oct 26 00:00:00
            var withdrawal_Day3_End = dispenserInbox.CreateWithdrawal(now.Date.AddDays(1).AddSeconds(-1), 5, 5, 50, "dest5", "addr5", domain, 1, "ext5", rate: 110000, receivedAmount: 10, receivedCurrency: "USD"); // Oct 26 23:59:59
            var withdrawal_Day4 = dispenserInbox.CreateWithdrawal(now.AddDays(1), 6, 6, 60, "dest6", "addr6", domain, 1, "ext6", rate: 110000, receivedAmount: 10, receivedCurrency: "USD"); // Oct 27

            var startDate = now.AddDays(-1).Date; // Oct 25
            var endDate = now.Date; // Oct 26

            // Act
            var result = dispenserInbox.WithdrawalsBy(startDate, endDate).ToList();

            // Assert
            Assert.AreEqual(4, result.Count, "Should find 4 withdrawals within the date range.");
            Assert.IsTrue(result.Any(w => w.Id == withdrawal_Day2_Start.Id), "Should include withdrawal from the start of the start date.");
            Assert.IsTrue(result.Any(w => w.Id == withdrawal_Day2_End.Id), "Should include withdrawal from the end of the start date.");
            Assert.IsTrue(result.Any(w => w.Id == withdrawal_Day3_Start.Id), "Should include withdrawal from the start of the end date.");
            Assert.IsTrue(result.Any(w => w.Id == withdrawal_Day3_End.Id), "Should include withdrawal from the end of the end date.");
            Assert.IsFalse(result.Any(w => w.Id == withdrawal_Day1.Id), "Should not include withdrawal from before the start date.");
            Assert.IsFalse(result.Any(w => w.Id == withdrawal_Day4.Id), "Should not include withdrawal from after the end date.");
        }

        [TestMethod]
        public void TestDispenserInbox_WithdrawalsBy_NoMatchingWithdrawals()
        {
            // Arrange
            CurrenciesTest.AddCurrencies();
            LiquidFlow.ClearInstance();
            DateTime now = new DateTime(2023, 10, 26);
            bool itIsThePresent = false;
            Liquid liquidBTC = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "BTC");
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);
            var dispenserInbox = liquidBTC.Outlet.DispenserInbox;

            // Create a withdrawal outside the query range
            dispenserInbox.CreateWithdrawal(now.AddDays(-5), 1, 1, 10, "dest1", "addr1", domain, 1, "ext1", rate: 110000, receivedAmount: 10, receivedCurrency: "USD");

            // Act
            var result = dispenserInbox.WithdrawalsBy(now.AddDays(-2), now.AddDays(-1));

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(0, result.Count());
        }

        [TestMethod]
        public void TestDispenserInbox_WithdrawalsBy_EmptyInbox()
        {
            // Arrange
            CurrenciesTest.AddCurrencies();
            LiquidFlow.ClearInstance();
            DateTime now = DateTime.Now;
            bool itIsThePresent = false;
            Liquid liquidBTC = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "BTC");
            var dispenserInbox = liquidBTC.Outlet.DispenserInbox;

            // Act
            var result = dispenserInbox.WithdrawalsBy(now.AddDays(-1), now);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(0, result.Count());
        }

        [TestMethod]
        public void TestDispenserInbox_WithdrawalsBy_ThrowsExceptionsForInvalidArguments()
        {
            // Arrange
            CurrenciesTest.AddCurrencies();
            LiquidFlow.ClearInstance();
            DateTime now = DateTime.Now;
            bool itIsThePresent = false;
            Liquid liquidBTC = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "BTC");
            var dispenserInbox = liquidBTC.Outlet.DispenserInbox;

            // Act & Assert
            var ex1 = Assert.ThrowsException<ArgumentNullException>(() => dispenserInbox.WithdrawalsBy(DateTime.MinValue, now));
            Assert.AreEqual("startDate", ex1.ParamName);

            var ex2 = Assert.ThrowsException<ArgumentNullException>(() => dispenserInbox.WithdrawalsBy(now, DateTime.MinValue));
            Assert.AreEqual("endDate", ex2.ParamName);

            var ex3 = Assert.ThrowsException<ArgumentException>(() => dispenserInbox.WithdrawalsBy(now, now.AddDays(-1)));
            Assert.AreEqual("End date cannot be earlier than start date. (Parameter 'endDate')", ex3.Message);
        }

        [TestMethod]
        public async Task TestCreateDispenserWithdrawalAsync()
        {
            CurrenciesTest.AddCurrencies();

            string txid = "h1h77has7as7";
            int domainId = 1;

            DateTime now = DateTime.Now;
            LiquidFlow.ClearInstance();
            bool itIsThePresent = false;
            Liquid liquidBTC = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "BTC");
            liquidBTC.Source.AddXpub(new Xpub("dasfsdfdsfsd"));
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);
            liquidBTC.Source.Liquid.ParentFlow.AddPaymentDock(domain, "ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag");
            var deposit1 = liquidBTC.Source.CreateDraftDeposit(itIsThePresent, now, liquidBTC.Source.NextDepositId(), "invoice123", 1, 22, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(liquidBTC.Source.Amount == 0);

            liquidBTC.Source.ConfirmDeposit(itIsThePresent, now, deposit1);
            Assert.IsTrue(liquidBTC.Source.Amount == 1);

            int tankId = liquidBTC.Source.NextTankId();
            int nextJarVerion = liquidBTC.Source.NextJarVersion();

            var tank = liquidBTC.Source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank1", "Fondos para navidad", nextJarVerion, liquidBTC.Source.Jar.ConfirmedDeposits());
            Assert.IsTrue(liquidBTC.Source.Amount == 1);
            Assert.IsTrue(tank != null);
            Assert.IsTrue(tank.Id == tankId);
            Assert.IsTrue(tank.Name == "Tank1");
            Assert.IsTrue(tank.Version == 1);
            Assert.IsTrue(tank.Amount == 1);

            int tankerId = liquidBTC.Source.NextTankerId();
            var tanker = liquidBTC.Source.CreateTanker(itIsThePresent, now, tankerId, "Tanker 1", "Description", new List<int> { tank.Id });
            Assert.IsTrue(tanker != null);
            Assert.IsTrue(tanker.Id == tankerId);
            Assert.IsTrue(tanker.Name == "Tanker 1");
            Assert.IsTrue(tanker.Description == "Description");
            Assert.IsTrue(tanker.Amount == 1);

            var tankerSeal = tanker.Sealed();
            Assert.IsTrue(liquidBTC.Source.Amount == 1);
            Assert.IsTrue(tankerSeal != null);
            Assert.IsTrue(tankerSeal.Id == tanker.Id);
            Assert.IsTrue(tankerSeal.Name == tanker.Name);
            Assert.IsTrue(tankerSeal.Description == tanker.Description);
            Assert.IsTrue(tankerSeal.Amount == tanker.Amount);

            int dispenserId = liquidBTC.Outlet.NextDispenserId();
            bool existDispenser = liquidBTC.Outlet.ExistDispenser(dispenserId);
            Assert.IsFalse(existDispenser, "Dispenser should not exist before creation");

            var dispenser = liquidBTC.Outlet.CreateDispenser(itIsThePresent, now, dispenserId, "MiDispenser", "description", now.AddDays(10));
            existDispenser = liquidBTC.Outlet.ExistDispenser(dispenserId);
            Assert.IsTrue(existDispenser, "Dispenser should exist after creation");

            int withdrawalId = liquidBTC.Outlet.NextWithdrawalId();
            int bottleId = liquidBTC.Outlet.NextBottleId();
            decimal amount = 10;
            string address = "tb1q4..."; // Replace with a valid address for testing purposes

            dispenserId = liquidBTC.Outlet.NextDispenserId();
            existDispenser = liquidBTC.Outlet.ExistDispenser(dispenserId);
            Assert.IsFalse(existDispenser, "Dispenser should not exist before creation");

            string destionation = "tb1q4...";
            string reference = "abc123";
            string externalReference = "external123";
            string atAddress = "cr1234";
            var withdrawal = liquidBTC.Outlet.CreateWithdrawal(itIsThePresent, now, withdrawalId, 1, amount, destionation, atAddress, domain, 1, externalReference, rate: 110000, receivedAmount: 10, receivedCurrency: "USD");
            liquidBTC.Outlet.DispenserInbox.ExplandedWithdrawals.Count();
            List<int> withdrawals = new List<int> { withdrawal.Id };
            if (!liquidBTC.Outlet.DispenserInbox.HasWithdrawal(withdrawal.Id))
            {
                throw new Exception("The withdrawal should exist in the inbox before moving to dispenser");
            }
            var enclosure = liquidBTC.Outlet.DispenserInbox.FindEnclosureWithdrawal(withdrawal.Id);
            if(enclosure == null)
            {
                throw new Exception("The enclosure should not be null when finding the withdrawal in the inbox");
            }
            if(enclosure.IsClaimed)
            {
                throw new Exception("The enclosure should not be claimed before moving to dispenser");
            }
            var dispenser2 = liquidBTC.Outlet.CreateDispenser(itIsThePresent, now, dispenserId, "Dispenser 2", "description",withdrawals);
            Assert.IsTrue(dispenser2.Withdrawals.Count() == 1, "The second dispenser should have one withdrawal after creation with withdrawals list");
        }


        [TestMethod]
        public async Task TestCreateDispenserWithDispenserAsync()
        {
            CurrenciesTest.AddCurrencies();

            string txid = "h1h77has7as7";
            int domainId = 1;

            DateTime now = DateTime.Now;
            LiquidFlow.ClearInstance();
            bool itIsThePresent = false;
            Liquid liquidBTC = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "BTC");
            liquidBTC.Source.AddXpub(new Xpub("dasfsdfdsfsd"));
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);
            liquidBTC.Source.Liquid.ParentFlow.AddPaymentDock(domain, "ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag");
            var deposit1 = liquidBTC.Source.CreateDraftDeposit(itIsThePresent, now, liquidBTC.Source.NextDepositId(), "invoice123", 1, 22, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(liquidBTC.Source.Amount == 0);

            liquidBTC.Source.ConfirmDeposit(itIsThePresent, now, deposit1);
            Assert.IsTrue(liquidBTC.Source.Amount == 1);

            int tankId = liquidBTC.Source.NextTankId();
            int nextJarVerion = liquidBTC.Source.NextJarVersion();

            var tank = liquidBTC.Source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank1", "Fondos para navidad", nextJarVerion, liquidBTC.Source.Jar.ConfirmedDeposits());
            Assert.IsTrue(liquidBTC.Source.Amount == 1);
            Assert.IsTrue(tank != null);
            Assert.IsTrue(tank.Id == tankId);
            Assert.IsTrue(tank.Name == "Tank1");
            Assert.IsTrue(tank.Version == 1);
            Assert.IsTrue(tank.Amount == 1);

            int tankerId = liquidBTC.Source.NextTankerId();
            var tanker = liquidBTC.Source.CreateTanker(itIsThePresent, now, tankerId, "Tanker 1", "Description", new List<int> { tank.Id });
            Assert.IsTrue(tanker != null);
            Assert.IsTrue(tanker.Id == tankerId);
            Assert.IsTrue(tanker.Name == "Tanker 1");
            Assert.IsTrue(tanker.Description == "Description");
            Assert.IsTrue(tanker.Amount == 1);

            var tankerSeal = tanker.Sealed();
            Assert.IsTrue(liquidBTC.Source.Amount == 1);
            Assert.IsTrue(tankerSeal != null);
            Assert.IsTrue(tankerSeal.Id == tanker.Id);
            Assert.IsTrue(tankerSeal.Name == tanker.Name);
            Assert.IsTrue(tankerSeal.Description == tanker.Description);
            Assert.IsTrue(tankerSeal.Amount == tanker.Amount);

            int dispenserId = liquidBTC.Outlet.NextDispenserId();
            bool existDispenser = liquidBTC.Outlet.ExistDispenser(dispenserId);
            Assert.IsFalse(existDispenser, "Dispenser should not exist before creation");

            var dispenser = liquidBTC.Outlet.CreateDispenser(itIsThePresent, now, dispenserId, "MiDispenser 1", "description", now.AddDays(10));
            existDispenser = liquidBTC.Outlet.ExistDispenser(dispenserId);
            Assert.IsTrue(existDispenser, "Dispenser should exist after creation");

            int withdrawalId = liquidBTC.Outlet.NextWithdrawalId();
            int bottleId = liquidBTC.Outlet.NextBottleId();
            decimal amount = 10;
            string address = "tb1q4..."; // Replace with a valid address for testing purposes

            dispenserId = liquidBTC.Outlet.NextDispenserId();
            existDispenser = liquidBTC.Outlet.ExistDispenser(dispenserId);
            Assert.IsFalse(existDispenser, "Dispenser should not exist before creation");

            string destionation = "tb1q4...";
            string reference = "abc123";
            string externalReference = "external123";
            string atAddress = "cr1234";
            var withdrawal = liquidBTC.Outlet.CreateWithdrawal(itIsThePresent, now, withdrawalId, 1, amount, destionation, atAddress, domain, 1, externalReference, rate: 110000, receivedAmount: 10, receivedCurrency: "USD");
            liquidBTC.Outlet.DispenserInbox.ExplandedWithdrawals.Count();
            List<int> withdrawals = new List<int> { withdrawal.Id };
            if (!liquidBTC.Outlet.DispenserInbox.HasWithdrawal(withdrawal.Id))
            {
                throw new Exception("The withdrawal should exist in the inbox before moving to dispenser");
            }
            var enclosure = liquidBTC.Outlet.DispenserInbox.FindEnclosureWithdrawal(withdrawal.Id);
            if (enclosure == null)
            {
                throw new Exception("The enclosure should not be null when finding the withdrawal in the inbox");
            }
            if (enclosure.IsClaimed)
            {
                throw new Exception("The enclosure should not be claimed before moving to dispenser");
            }
            var dispenser2 = liquidBTC.Outlet.CreateDispenser(itIsThePresent, now, dispenserId, "Dispenser 2", "description", withdrawals);
            Assert.IsTrue(dispenser2.Withdrawals.Count() == 1, "The second dispenser should have one withdrawal after creation with withdrawals list");
            var dispenserIds = new List<int> { dispenser.Id, dispenser2.Id };
            dispenserId = liquidBTC.Outlet.NextDispenserId();

            var dispenser3 = liquidBTC.Outlet.CreateDispenserWithDispenser(itIsThePresent, now, dispenserId, "Dispenser 3", "description", now.AddDays(10),dispenserIds);
            Assert.IsTrue(dispenser3.ExplandedWithdrawals.Count() == 1, "The third dispenser should have two withdrawals after creation with two dispensers' withdrawals list");
            Assert.IsTrue(dispenser3.DescendantDispensers(false).Count() == 2, "The third dispenser should have two descendant dispensers");
            dispenserId = liquidBTC.Outlet.NextDispenserId();
            dispenserIds = new List<int> { dispenser3.Id};
            var dispenser4 = liquidBTC.Outlet.CreateDispenserWithDispenser(itIsThePresent, now, dispenserId, "Dispenser 4", "description", dispenserIds);
            Assert.IsTrue(dispenser4.ExplandedWithdrawals.Count() == 1, "The third dispenser should have two withdrawals after creation with two dispensers' withdrawals list");
            Assert.IsTrue(dispenser4.DescendantDispensers(false).Count() == 3, "The third dispenser should have two descendant dispensers");

        }

        [TestMethod]
        public async Task TestFiltersDispensersAsync()
        {
            CurrenciesTest.AddCurrencies();

            string txid = "h1h77has7as7";
            int domainId = 1;

            DateTime now = DateTime.Now;
            LiquidFlow.ClearInstance();
            bool itIsThePresent = false;
            Liquid liquidBTC = LiquidFlow.Instance(new List<Domain>()).GetOrCreateLiquid(itIsThePresent, now, "BTC");
            liquidBTC.Source.AddXpub(new Xpub("dasfsdfdsfsd"));
            var domain = new Domain(false, 1, "localhost", Agents.TEST_BOOK);
            liquidBTC.Source.Liquid.ParentFlow.AddPaymentDock(domain, "ALSYBxGCpcEozJ6oTXc64HJGedenXtWb54krX5aKeWag");
            var deposit1 = liquidBTC.Source.CreateDraftDeposit(itIsThePresent, now, liquidBTC.Source.NextDepositId(), "invoice123", 1, 22, "tb1q4...", 1M, 100000M, "USD", 100, 1, 1, domain);
            Assert.IsTrue(liquidBTC.Source.Amount == 0);

            liquidBTC.Source.ConfirmDeposit(itIsThePresent, now, deposit1);
            Assert.IsTrue(liquidBTC.Source.Amount == 1);

            int tankId = liquidBTC.Source.NextTankId();
            int nextJarVerion = liquidBTC.Source.NextJarVersion();

            var tank = liquidBTC.Source.Jar.CreateTank(itIsThePresent, now, tankId, "Tank1", "Fondos para navidad", nextJarVerion, liquidBTC.Source.Jar.ConfirmedDeposits());
            Assert.IsTrue(liquidBTC.Source.Amount == 1);
            Assert.IsTrue(tank != null);
            Assert.IsTrue(tank.Id == tankId);
            Assert.IsTrue(tank.Name == "Tank1");
            Assert.IsTrue(tank.Version == 1);
            Assert.IsTrue(tank.Amount == 1);

            int tankerId = liquidBTC.Source.NextTankerId();
            var tanker = liquidBTC.Source.CreateTanker(itIsThePresent, now, tankerId, "Tanker 1", "Description", new List<int> { tank.Id });
            Assert.IsTrue(tanker != null);
            Assert.IsTrue(tanker.Id == tankerId);
            Assert.IsTrue(tanker.Name == "Tanker 1");
            Assert.IsTrue(tanker.Description == "Description");
            Assert.IsTrue(tanker.Amount == 1);

            var tankerSeal = tanker.Sealed();
            Assert.IsTrue(liquidBTC.Source.Amount == 1);
            Assert.IsTrue(tankerSeal != null);
            Assert.IsTrue(tankerSeal.Id == tanker.Id);
            Assert.IsTrue(tankerSeal.Name == tanker.Name);
            Assert.IsTrue(tankerSeal.Description == tanker.Description);
            Assert.IsTrue(tankerSeal.Amount == tanker.Amount);

            int dispenserId = liquidBTC.Outlet.NextDispenserId();
            bool existDispenser = liquidBTC.Outlet.ExistDispenser(dispenserId);
            Assert.IsFalse(existDispenser, "Dispenser should not exist before creation");

            var dispenser = liquidBTC.Outlet.CreateDispenser(itIsThePresent, now, dispenserId, "MiDispenser 1", "description", now.AddDays(10));
            existDispenser = liquidBTC.Outlet.ExistDispenser(dispenserId);
            Assert.IsTrue(existDispenser, "Dispenser should exist after creation");

            int withdrawalId = liquidBTC.Outlet.NextWithdrawalId();
            int bottleId = liquidBTC.Outlet.NextBottleId();
            decimal amount = 10;
            string address = "tb1q4..."; // Replace with a valid address for testing purposes

            dispenserId = liquidBTC.Outlet.NextDispenserId();
            existDispenser = liquidBTC.Outlet.ExistDispenser(dispenserId);
            Assert.IsFalse(existDispenser, "Dispenser should not exist before creation");

            string destionation = "tb1q4...";
            string reference = "abc123";
            string externalReference = "external123";
            string atAddress = "cr1234";
            var withdrawal = liquidBTC.Outlet.CreateWithdrawal(itIsThePresent, now, withdrawalId, 1, amount, destionation, atAddress, domain, 1, externalReference, rate: 110000, receivedAmount: 10, receivedCurrency: "USD");
            liquidBTC.Outlet.DispenserInbox.ExplandedWithdrawals.Count();
            List<int> withdrawals = new List<int> { withdrawal.Id };
            if (!liquidBTC.Outlet.DispenserInbox.HasWithdrawal(withdrawal.Id))
            {
                throw new Exception("The withdrawal should exist in the inbox before moving to dispenser");
            }
            var enclosure = liquidBTC.Outlet.DispenserInbox.FindEnclosureWithdrawal(withdrawal.Id);
            if (enclosure == null)
            {
                throw new Exception("The enclosure should not be null when finding the withdrawal in the inbox");
            }
            if (enclosure.IsClaimed)
            {
                throw new Exception("The enclosure should not be claimed before moving to dispenser");
            }
            var dispenser2 = liquidBTC.Outlet.CreateDispenser(itIsThePresent, now, dispenserId, "Dispenser 2", "description", withdrawals);
            Assert.IsTrue(dispenser2.Withdrawals.Count() == 1, "The second dispenser should have one withdrawal after creation with withdrawals list");
            var dispenserIds = new List<int> { dispenser.Id, dispenser2.Id };
            dispenserId = liquidBTC.Outlet.NextDispenserId();

            var dispenser3 = liquidBTC.Outlet.CreateDispenserWithDispenser(itIsThePresent, now, dispenserId, "Dispenser 3", "description", now.AddDays(10), dispenserIds);
            var summary = dispenser3.BuildMonthlySummary(DateTime.MinValue, DateTime.MaxValue, "Dispenser 2", FilterContainerType.ALL,"");
            Assert.IsTrue(summary.MonthlySummaries.Count() == 1, "The summary should have one entry for Dispenser 2");
            summary = dispenser3.BuildMonthlySummary(DateTime.MinValue, DateTime.MaxValue, "", FilterContainerType.TRANSACTION, "");
            Assert.IsTrue(summary.MonthlySummaries.Count() == 0, "The summary should have 0 entry for TRANSACTION");
            summary = dispenser3.BuildMonthlySummary(DateTime.MinValue, DateTime.MaxValue, "", FilterContainerType.CONTAINER, "");
            Assert.IsTrue(summary.MonthlySummaries.Count() == 1, "The summary should have one entry for CONTAINER");

            Assert.IsTrue(dispenser3.ExplandedWithdrawals.Count() == 1, "The third dispenser should have two withdrawals after creation with two dispensers' withdrawals list");
            Assert.IsTrue(dispenser3.DescendantDispensers(false).Count() == 2, "The third dispenser should have two descendant dispensers");
            dispenserId = liquidBTC.Outlet.NextDispenserId();
            dispenserIds = new List<int> { dispenser3.Id };
            var dispenser4 = liquidBTC.Outlet.CreateDispenserWithDispenser(itIsThePresent, now, dispenserId, "Dispenser 4", "description", dispenserIds);
            Assert.IsTrue(dispenser4.ExplandedWithdrawals.Count() == 1, "The third dispenser should have two withdrawals after creation with two dispensers' withdrawals list");
            Assert.IsTrue(dispenser4.DescendantDispensers(false).Count() == 3, "The third dispenser should have two descendant dispensers");
            var dispensers = liquidBTC.Outlet.BuildDispenserWithWithdrawalsBetween(now.AddDays(-1), now.AddDays(1),FilterContainerStatus.READY, "Dispenser 2", "");
            Assert.IsTrue(dispensers.Count() == 1, "There should be one dispenser with READY status and name filter 'Dispenser 2'");
            var currentDisepnser = dispensers.First();
            Assert.IsTrue(currentDisepnser.Withdrawals.Count() == 1, "The dispenser should have one withdrawal");
            Assert.IsTrue(currentDisepnser.Name == "Dispenser 2", "The dispenser name should be 'Dispenser 2'");
        }
    }
}
