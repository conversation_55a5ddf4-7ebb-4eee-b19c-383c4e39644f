﻿using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using System;

namespace GamesEngine.Business.Liquidity.Transactions
{
    internal class EnclosureDeposit : Enclosure
    {
        internal EnclosureDeposit(Deposit deposit, int tankVersion) : base(deposit, tankVersion)
        {
        }

        internal int TankVersion => ContainerVersion;

        internal int Id => Transaction.Id;

        internal Deposit Deposit => (Deposit)Transaction;

        internal decimal Amount => Deposit.Amount;

        internal decimal ConfirmedAmount => Deposit.ConfirmedAmount;

        internal DateTime CreatedAt => Deposit.CreatedAt;
        internal DateTime ConfirmedDate { get; private set; }
        internal DateTime CanceledDated { get; private set; }

        internal bool IsPending => ConfirmedDate == DateTime.MinValue && CanceledDated == DateTime.MinValue;
        internal bool IsConfirmed => ConfirmedDate != DateTime.MinValue && CanceledDated == DateTime.MinValue;
        internal bool IsCanceled => CanceledDated != DateTime.MinValue;

        internal string TransactionId { get; private set; } = string.Empty;

        internal void SetTransactionId(string transactionId)
        {
            if (string.IsNullOrWhiteSpace(transactionId)) throw new ArgumentNullException(nameof(transactionId));
            if (!string.IsNullOrWhiteSpace(TransactionId)) throw new InvalidOperationException("TransactionId is already set.");
            TransactionId = transactionId;
        }

        internal void Confirm(DateTime confirmedDate)
        {
            if (confirmedDate == DateTime.MinValue) throw new ArgumentNullException(nameof(confirmedDate));
            if (IsConfirmed) throw new InvalidOperationException("Deposit is already confirmed.");
            ConfirmedDate = confirmedDate;

            DepositConfirmedEvent depositConfirmedEvent = new DepositConfirmedEvent(Id);
            PlatformMonitor.GetInstance().WhenNewEvent(depositConfirmedEvent);
        }

        internal void Cancel(DateTime canceledDate)
        {
            if (canceledDate == DateTime.MinValue) throw new ArgumentNullException(nameof(canceledDate));
            if (IsCanceled) throw new GameEngineException("Deposit is already canceled.");
            CanceledDated = canceledDate;

            DepositCanceledEvent depositCanceledEvent = new DepositCanceledEvent(Id);
            PlatformMonitor.GetInstance().WhenNewEvent(depositCanceledEvent);

            bool itIsThePresent = ExecutionContext.Current.ItIsThePresent;
            if (itIsThePresent)
            {
                if (Integration.UseKafka)
                {
                    using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, Integration.Kafka.TopicForContainerEvents))
                    {
                        CanceledDepositMessage canceledDepositMsg = new CanceledDepositMessage(Deposit.Id, canceledDate);
                        buffer.Send(canceledDepositMsg);
                    }
                }
            }
        }
    }
}
