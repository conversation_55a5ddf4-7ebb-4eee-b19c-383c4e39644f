﻿using GamesEngine.Business.Liquidity.Sentinels.Inbound;
using GamesEngine.Business.Liquidity.Sentinels.Outbound;
using Puppeteer.EventSourcing;
using Puppeteer.EventSourcing.Libraries;
using System;

namespace GamesEngine.Business.Liquidity.Sentinels
{

    internal abstract class Sentinel : Objeto
    {
        protected readonly SentinelTasks sentinelTasks;

        protected Sentinel()
        {
            sentinelTasks = new SentinelTasks();
        }

        internal SentinelTask FindTask(object objectKey)
        {
            if (objectKey == null) throw new ArgumentNullException(nameof(objectKey));
            return sentinelTasks.FindTask(objectKey);
        }

        internal bool HasTask(object objectKey)
        {
            if (objectKey == null) throw new ArgumentNullException(nameof(objectKey));
            return sentinelTasks.HasTask(objectKey);
        }

        internal void Detach(SentinelTask task)
        {
            if (task == null) throw new ArgumentNullException(nameof(task));
            sentinelTasks.Detach(task);
        }

        internal void StartSentinelTasks(DateTime now)
        {
            if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now));

            foreach (var task in sentinelTasks.Tasks)
            {
                try
                {
                    // SKIP DEPOSIT IF IT HAS NO INVOICE
                    if (task is ConfirmationsInboundTask confirmationsInboundTask && confirmationsInboundTask.PaymentInvoice == null) continue;

                    task.StartTask(now);
                }
                catch(Exception ex)
                {
                    switch (task)
                    {
                        case ConfirmationsInboundTask confirmationsInboundTask:
                            Loggers.GetIntance().Sentinel.Error($"Failed to start sentinel type {nameof(ConfirmationsInboundTask)} for deposit id {confirmationsInboundTask.Deposit.Id}.", ex);
                            break;
                        case RateInboundTask rateInboundTask:
                            Loggers.GetIntance().Sentinel.Error($"Failed to start sentinel type {nameof(RateInboundTask)} for kind {rateInboundTask.Kind}.", ex);
                            break;
                        case TankerInboundTask tankerInboundTask:
                            Loggers.GetIntance().Sentinel.Error($"Failed to start sentinel type {nameof(TankerInboundTask)} for tanker id {tankerInboundTask.Tanker.Id}.", ex);
                            break;
                        case ConfirmationsOutboundTask confimationsOutboundTask:
                            Loggers.GetIntance().Sentinel.Error($"Failed to start sentinel type {nameof(ConfirmationsOutboundTask)} for dispenser id {confimationsOutboundTask.Dispenser.Id}.", ex);
                            break;
                    }
                }

            }
        }
    }
}
