﻿using GamesEngine.Business.Liquidity.Transactions;
using GamesEngine.MessageQueuing;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using System;
using System.Threading;
using System.Threading.Tasks;
using static GamesEngine.Business.Liquidity.PaymentEngineDocks;
using static GamesEngine.Settings.PaymentManager;

namespace GamesEngine.Business.Liquidity.Sentinels.Inbound
{
    internal class ConfirmationsInboundTask : InboundTask
    {
        internal Deposit Deposit { get; private set; }

        internal int TotalConfirmations { get; private set; }

        internal int CurrentConfirmations { get; private set; }

        internal PaymentInvoice PaymentInvoice { get; private set; }

        internal bool HasPaymentInvoice => PaymentInvoice != null;

        internal SentinelTasks SentinelInboundTasks { get; private set; }

        internal CancellationTokenSource Cancellation { get; private set; } = new();

        internal PaymentEngineDock PaymentEngineDock { get; private set; }

        internal ConfirmationsInboundTask(SentinelTasks inboundTasks, Deposit deposit, int totalConfirmations, PaymentEngineDock paymentEngineDock)
        {
            if (inboundTasks == null) throw new ArgumentNullException(nameof(inboundTasks));
            if (deposit == null) throw new ArgumentNullException(nameof(deposit));
            if (totalConfirmations < 0) throw new ArgumentNullException(nameof(totalConfirmations));
            if (paymentEngineDock == null) throw new ArgumentNullException(nameof(paymentEngineDock));

            SentinelInboundTasks = inboundTasks;
            Deposit = deposit;
            TotalConfirmations = totalConfirmations;
            PaymentEngineDock = paymentEngineDock;
        }

        internal void InitializeInvoice(string id, decimal invoiceDue, decimal totalPaid, decimal rate, string paymentId, string invoceStoreId)
        {
            if (string.IsNullOrWhiteSpace(id)) throw new ArgumentNullException(nameof(id));
            if (totalPaid < 0) throw new ArgumentNullException(nameof(totalPaid));
            if (rate < 0) throw new ArgumentNullException(nameof(rate));
            if (string.IsNullOrWhiteSpace(paymentId)) throw new ArgumentNullException(nameof(paymentId));
            if (string.IsNullOrWhiteSpace(invoceStoreId)) throw new ArgumentNullException(nameof(invoceStoreId));

            PaymentInvoice = new PaymentInvoice(id, invoiceDue, totalPaid, rate, paymentId, invoceStoreId);
        }

        internal override void StartTask(DateTime startedAt)
        {
            if (PaymentInvoice == null) throw new ArgumentNullException(nameof(PaymentInvoice));
            if (task == null)
            {
                task = Task.Run(async () =>
                {
                    while (CurrentConfirmations < TotalConfirmations && !Cancellation.IsCancellationRequested)
                    {
                        await Task.Delay(SentinelTasks.DELAY_SECONDS_PER_CONFIRMATION * 1000);                        
                        try
                        {
                            CurrentConfirmations = await PaymentManager.TotalConfirmatiosAsync(PaymentInvoice);
                        }
                        catch(Exception e)
                        {
                            Loggers.GetIntance().Sentinel.Error($"Failed to get confirmations for invoice {PaymentInvoice.Id}.", e);
                        }
                    }
                    if (Cancellation.IsCancellationRequested) return;

                    if (Integration.UseKafka)
                    {
                        using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(true, Integration.Kafka.TopicForLiquidityEvents))
                        {
                            ConfirmedDepositDueBody msgConfirmDeposit = new ConfirmedDepositDueBody(
                                kind: Deposit.Kind,
                                depositId: Deposit.Id,
                                totalPaid: PaymentInvoice.TotalPaid,
                                rate: PaymentInvoice.Rate,
                                due: PaymentInvoice.Due
                            );
                            buffer.Send(msgConfirmDeposit);
                        }
                    }
                });
            }
        }

        public override string ToString()
        {
            string status = string.Empty;
            if (task == null)
            {
                status = "Not started";
            }
            else if (task.IsCompleted)
            {
                status = "Completed";
            }
            else if (task.IsCanceled)
            {
                status = "Canceled";
            }
            else if (task.IsFaulted)
            {
                status = "Faulted";
            }
            else
            {
                status = "Running";
            }
            return $"Liquidity Deposit ID {Deposit.Id} Has Risk Confirmations: {TotalConfirmations}, Current Confirmations: {CurrentConfirmations} and Task Status: {status}";
        }
    }
    public class ConfirmedDepositDueBody : TypedMessage
    {
        public string Kind { get; set; }
        public int DepositId { get; set; }
        public decimal TotalPaid { get; set; }
        public decimal Rate { get; set; }
        public decimal Due { get; set; }

        public ConfirmedDepositDueBody(string kind, int depositId, decimal totalPaid, decimal rate, decimal due) : base((char)LiquidityMessageType.SentinelConfirmDeposit)
        {
            if (string.IsNullOrWhiteSpace(kind)) throw new ArgumentNullException(nameof(kind));
            if (depositId <= 0) throw new ArgumentNullException(nameof(depositId));
            if (totalPaid <= 0) throw new ArgumentNullException(nameof(totalPaid));
            if (rate <= 0) throw new ArgumentNullException(nameof(rate));
            if (due < 0) throw new ArgumentNullException(nameof(due));

            Kind = kind;
            DepositId = depositId;
            TotalPaid = totalPaid;
            Rate = rate;
            Due = due;
        }

        public ConfirmedDepositDueBody(string serializedMessage) : base(serializedMessage) { }

        protected override void Deserialize(string[] serializedMessage, out int fieldOrder)
        {
            if (serializedMessage.Length <= 0) throw new GameEngineException("Invalid format to make a deposit");
            base.Deserialize(serializedMessage, out fieldOrder);

            Kind = serializedMessage[fieldOrder++];
            DepositId = int.Parse(serializedMessage[fieldOrder++]);
            TotalPaid = decimal.Parse(serializedMessage[fieldOrder++]);
            Rate = decimal.Parse(serializedMessage[fieldOrder++]);
            Due = decimal.Parse(serializedMessage[fieldOrder++]);
        }

        protected override void InternalSerialize()
        {
            base.InternalSerialize();
            AddProperty(Kind).
            AddProperty(DepositId).
            AddProperty(TotalPaid).
            AddProperty(Rate).
            AddProperty(Due);
        }
    }
}
