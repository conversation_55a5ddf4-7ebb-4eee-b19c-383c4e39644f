﻿using GamesEngine.Business.Liquidity.Containers;
using GamesEngine.Finance;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using System;
using System.Threading.Tasks;

namespace GamesEngine.Business.Liquidity.Sentinels.Inbound
{
    internal class RateInboundTask : InboundTask
    {
        internal string Kind { get; private set; }

        internal decimal CurrentRate { get; private set; }

        internal DateTime LastUpdate { get; private set; }

        internal RateInboundTask(string kind)
        {
            if (string.IsNullOrWhiteSpace(kind)) throw new ArgumentNullException(nameof(kind));
            Kind = kind;
        }

        private const int RATE_FETCH_INTERVAL_SECONDS = 60;
        internal override void StartTask(DateTime startedAt)
        {
            if (task == null)
            {
                task = Task.Run(async () =>
                {
                    while (true)
                    {
                        try
                        {
                            var rateResult = await PaymentManager.ExchangeRateAsync(Kind, $"{Currencies.CODES.USD}");
                            bool needToUpdateRate = CurrentRate == 0 || rateResult.Rate != CurrentRate;
                            if (needToUpdateRate)
                            {
                                CurrentRate = rateResult.Rate;
                                LastUpdate = DateTime.Now;
                                if (Integration.UseKafka)
                                {
                                    using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(true, Integration.Kafka.TopicForLiquidityEvents))
                                    {
                                        ExchangeRateChangeMessage message = new ExchangeRateChangeMessage(Kind, CurrentRate);
                                        buffer.Send(message);
                                    }
                                }
                            }
                            await Task.Delay(RATE_FETCH_INTERVAL_SECONDS * 1000);
                        }
                        catch (Exception ex)
                        {
                            Loggers.GetIntance().Sentinel.Error($"Failed to get rate for kind {Kind}.", ex);
                        }
                    }
                });
            }
        }

        internal class ExchangeRateChangeMessage : LiquidityEventMessage
        {
            internal string Kind { get; private set; }
            internal decimal Rate { get; private set; }

            internal ExchangeRateChangeMessage(string kind, decimal rate) : base(LiquidityMessageType.ExchangeRateChange)
            {
                if (string.IsNullOrWhiteSpace(kind)) throw new ArgumentNullException(nameof(kind));
                if (rate <= 0) throw new ArgumentNullException(nameof(rate));
                Kind = kind;
                Rate = rate;
            }

            internal ExchangeRateChangeMessage(string message) : base(message) { }

            protected override void Deserialize(string[] serializedMessage, out int fieldOrder)
            {
                base.Deserialize(serializedMessage, out fieldOrder);
                Kind = serializedMessage[fieldOrder++];
                Rate = decimal.Parse(serializedMessage[fieldOrder++]);
            }

            protected override void InternalSerialize()
            {
                base.InternalSerialize();
                AddProperty(Kind).
                AddProperty(Rate);
            }
        }
    }
}
