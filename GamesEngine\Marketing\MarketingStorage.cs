﻿using GamesEngine.Finance;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using MySql.Data.MySqlClient;
using Puppeteer.EventSourcing.Libraries;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GamesEngine.Domains;
using GamesEngine.Marketing.Campaigns.Base;
using static GamesEngine.Business.Marketing.MarketingStorage;
using GamesEngine.Games.Lines;
using GamesEngine.Marketing.Campaigns;

namespace GamesEngine.Business.Marketing
{
    public class MarketingStorage
    {

        private readonly LoyaltyStorage loyaltyStorage;

        public MarketingStorage(HistoricalDatabaseType dbType, string connectionString)
        {
            if (String.IsNullOrEmpty(connectionString)) throw new ArgumentNullException(nameof(connectionString));
            if (dbType == HistoricalDatabaseType.MySQL)
            {
                loyaltyStorage = new LoyaltyStorageMySQL(connectionString);
            }
            else if (dbType == HistoricalDatabaseType.SQLServer)
            {
                loyaltyStorage = new LoyaltyStorageSQLServer(connectionString);
            }
            else
            {
                throw new GameEngineException($"There is no {nameof(LoyaltyStorage)} implementation for connection string {connectionString}");
            }
        }

        internal void StoreAcquisition(int storeId, string customerIdentifier, string accountNumber, DateTime acquisitionDate)
        {
            if (String.IsNullOrWhiteSpace(customerIdentifier)) throw new ArgumentNullException(nameof(customerIdentifier));
            if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));
            if (loyaltyStorage == null) throw new GameEngineException("loyaltyStorage has not been set");

            loyaltyStorage.StoreAcquisition(storeId, customerIdentifier, accountNumber, acquisitionDate);
        }

        internal bool ExistCustomer(string accountNumber, int storeId)
        {
            if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));
            if (loyaltyStorage == null) throw new GameEngineException("loyaltyStorage has not been set");

            bool result = loyaltyStorage.ExistCustomer(accountNumber, storeId);

            return result;
        }

        internal void StoreRetention(int storeId, string customerIdentifier, string accountNumber, DateTime visitDate)
        {
            if (String.IsNullOrWhiteSpace(customerIdentifier)) throw new ArgumentNullException(nameof(customerIdentifier));
            if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));
            if (loyaltyStorage == null) throw new GameEngineException("loyaltyStorage has not been set");

            loyaltyStorage.StoreRetention(storeId, customerIdentifier, accountNumber, visitDate);
        }

        internal void StoreSale(int storeId, string customerIdentifier, string accountNumber, Currency amount, DateTime saleDate, int domainId)
        {
            if (String.IsNullOrWhiteSpace(customerIdentifier)) throw new ArgumentNullException(nameof(customerIdentifier));
            if (String.IsNullOrWhiteSpace(accountNumber)) throw new ArgumentNullException(nameof(accountNumber));
            if (amount == null) throw new ArgumentNullException(nameof(amount));
            if (loyaltyStorage == null) throw new GameEngineException("loyaltyStorage has not been set");
            if (domainId <= 0) throw new GameEngineException("domainId must be greater than 0");

            loyaltyStorage.StoreSale(storeId, customerIdentifier, accountNumber, amount, saleDate, domainId);
        }

        internal void StoreCurrency(CoinMessage msg)
        {
            if (msg == null) throw new ArgumentNullException(nameof(msg));

            loyaltyStorage.StoreCurrency(msg);
        }

        internal async Task<IEnumerable<AcquisitionDB>> FirstVisitForAsync()
        {
            return await loyaltyStorage.FirstVisitForAsync();
        }

        internal class AcquisitionDB
        {
            internal string CustomerIdentifier;
            internal string AccountNumber;
            internal DateTime AcquisitionDate;
            internal int StoreId;
        }

        internal class VisitDB
        {
            internal string CustomerIdentifier;
            internal string AccountNumber;
            internal DateTime VisitDate;
            internal int StoreId;
        }

        internal class SaleDB
        {
            internal string CustomerIdentifier;
            internal string AccountNumber;
            internal Currencies.CODES Currency;
            internal decimal Amount;
            internal DateTime SaleDate;
            internal int StoreId;
        }

        public abstract class LoyaltyStorage
        {
            protected readonly string connectionString;
            public const string TABLE_ACQUISITION = "Acquisition";
            public const string TABLE_VISITS = "Visits";
            public const string TABLE_SALES = "Sales";
            public const string TABLE_SALE_CAMPAIGNS = "SaleCampaings";
            public const string TABLE_SALE_CAMPAIGN_REWARDS = "SaleCampaignRewards";

            protected LoyaltyStorage(string connectionString)
            {
                this.connectionString = connectionString;
            }

            internal abstract void CrearStorage();
            internal abstract bool ExistTable(string tableName);
            internal abstract bool ExistColumnTable(string tableName, string columnName);
            internal abstract bool ExistCustomer(string accountNumber, int storeId);
            internal abstract bool ExistStreakCamping(int promotionNumber);
            internal abstract void StoreAcquisition(int storeId, string customerIdentifier, string accountNumber, DateTime acquisitionDate);
            internal abstract void StoreRetention(int storeId, string customerIdentifier, string accountNumber, DateTime visitDate);
            internal abstract void StoreCurrency(CoinMessage msg);
            internal abstract void StoreSale(int storeId, string customerIdentifier, string accountNumber, Currency amount, DateTime saleDate, int domainId);
            internal abstract Task<IEnumerable<AcquisitionDB>> FirstVisitForAsync();

            internal abstract void StoreCampaign(int promotionNumber, DateTime creationDate, string name, string applicableCurrency, string returnedCurrency, decimal maxAmount, bool budgetIsUnlimited, DateTime startDateCampaign, DateTime endDateCampaign, int maxLevels, string descripcion, DateTime lastDayToChangePrizes, string campaignType, string rewardType, string scaleType);
            internal abstract void UpdateCampaign(int promotionNumber, string name, string applicableCurrency, string returnedCurrency, Budget budget, DateTime startDateCampaign, DateTime endDateCampaign, int maxLevels, string descripcion, DateTime lastDayToChangePrizes, string rewardType, string scaleType);
            internal abstract void StoreCampaignReward(string playerIdentifier, int segment, string tittle, decimal prize, int level, DateTime now, int promotionNumber, int storeId, int domainId);
            internal abstract DataTable RewardTableFromQuery(int page, int pageSize, string accountNumber, int promotionNumber, out int totalRows);
            internal abstract DataTable PlayerSalesTable(int page, int pageSize, string accountNumber, string currencyCode, DateTime startDate, DateTime endDate, IEnumerable<Store> storeIds, IEnumerable<Domain> domainIds, out int totalRows);
        }

        private class LoyaltyStorageMySQL : LoyaltyStorage
        {
            internal LoyaltyStorageMySQL(string connectionString) : base(connectionString)
            {
                CrearStorage();
            }

            internal override bool ExistTable(string tableName)
            {
                // create a string builder for the statement for check if table exists
                StringBuilder statement = new StringBuilder();
                statement.Append("SELECT 1 FROM INFORMATION_SCHEMA.TABLES ")
                    .Append("WHERE TABLE_NAME = '" + tableName + "';");
                string sql = statement.ToString();
                // execute statement and get result
                bool exists = false;
                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (MySqlCommand command = new MySqlCommand(sql, connection))
                        {
                            var result = (int)command.ExecuteScalar();
                            exists = result == 1;
                        }
                    }
                    catch
                    {
                        exists = false;
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
                return exists;
            }

            internal override bool ExistColumnTable(string tableName, string columnName)
            {
                StringBuilder statement = new StringBuilder();
                string tableSchema = connectionString.Split(';').FirstOrDefault(s => s.Contains("Database")).Split('=')[1];
                statement.Append($"SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = '{tableSchema}' AND TABLE_NAME = '{tableName}' AND COLUMN_NAME = '{columnName}';");
                string sql = statement.ToString();
                bool exists = false;
                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (MySqlCommand command = new MySqlCommand(sql, connection))
                        {
                            object result = command.ExecuteScalar();
                            if(result != null)
                            {
                                exists = (long)result == 1;
                            }
                        }
                    }
                    catch
                    {
                        exists = false;
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
                return exists;
            }

            internal override void CrearStorage()
            {
                StringBuilder statement = new StringBuilder();

                statement.AppendLine($@"
				    CREATE TABLE IF NOT EXISTS currency(
					    id INT NOT NULL AUTO_INCREMENT,
					    sign VARCHAR(5) NOT NULL,
					    isoCode VARCHAR(5) NOT NULL,
					    uniCode VARCHAR(5) NOT NULL,
					    decimalPrecision TINYINT NOT NULL,
					    PRIMARY KEY(id));
                    SET sql_mode='NO_AUTO_VALUE_ON_ZERO';
				    INSERT IGNORE INTO currency(id, sign, isoCode, uniCode, decimalPrecision) VALUES
					    (0, '$', 'FP', '$', 2),
					    (2, '$', 'USD', '$', 2);
			    ");

                statement
                    .Append("create table IF NOT EXISTS ").Append(TABLE_ACQUISITION)
                    .Append("(")
                    .Append($"CUSTOMER VARCHAR({Customer.DATABASE_FIELD_IDENTIFIER_LENGHT}) NOT NULL,")
                    .Append($"ACCOUNT VARCHAR({Customer.DATABASE_FIELD_ACCOUNT_LENGHT}) NOT NULL,")
                    .Append("ACQUISITIONDATE DATETIME NOT NULL,")
                    .Append("STORE TINYINT UNSIGNED NOT NULL,")
                    .Append($"CONSTRAINT {TABLE_ACQUISITION}_PK PRIMARY KEY (ACCOUNT, STORE)")
                    .Append(") CHARSET=utf8;");

                statement
                    .Append("create table IF NOT EXISTS ").Append(TABLE_VISITS)
                    .Append("(")
                    .Append($"CUSTOMER VARCHAR({Customer.DATABASE_FIELD_IDENTIFIER_LENGHT}) NOT NULL,")
                    .Append($"ACCOUNT VARCHAR({Customer.DATABASE_FIELD_ACCOUNT_LENGHT}) NOT NULL,")
                    .Append("LASTVISITOFDAY DATETIME NOT NULL,")
                    .Append("VISITDATE DATETIME NOT NULL,")
                    .Append("VISITCOUNTOFDAY MEDIUMINT UNSIGNED NOT NULL,")
                    .Append("STORE TINYINT UNSIGNED NOT NULL,")
                    .Append($"CONSTRAINT {TABLE_VISITS}_PK PRIMARY KEY (ACCOUNT, LASTVISITOFDAY, STORE)")
                    .Append(") CHARSET=utf8;");

                statement
                    .Append("create table IF NOT EXISTS ").Append(TABLE_SALES)
                    .Append("(")
                    .Append($"CUSTOMER VARCHAR({Customer.DATABASE_FIELD_IDENTIFIER_LENGHT}) NOT NULL,")
                    .Append($"ACCOUNT VARCHAR({Customer.DATABASE_FIELD_ACCOUNT_LENGHT}) NOT NULL,")
                    .Append("CURRENCY TINYINT NOT NULL,")
                    .Append("AMOUNT DECIMAL(16,8) NOT NULL,")
                    .Append("SALEDATE DATETIME NOT NULL,")
                    .Append("STORE TINYINT UNSIGNED NOT NULL,")
                    .Append("DOMAIN INT NOT NULL,")
                    .Append($"CONSTRAINT {TABLE_SALES}_PK PRIMARY KEY (ACCOUNT, SALEDATE, STORE, CURRENCY, DOMAIN)")
                    .Append(") CHARSET=utf8;");

                statement.AppendLine($@"
                    CREATE TABLE IF NOT EXISTS {TABLE_SALE_CAMPAIGNS} (
                        PROMOTION INT NOT NULL,
                        CREATEDDATE DATETIME NOT NULL,
                        NAME VARCHAR(20) NOT NULL,
                        APPLICABLECURRENCY VARCHAR(5),
                        RETURNEDCURRENCY VARCHAR(5) NOT NULL,
                        MAXAMOUNT DECIMAL(16, 2),
                        STARTDATECAMPAIGN DATETIME NOT NULL,
                        ENDDATECAMPAIGN DATETIME NOT NULL,
                        MAXLEVELS INT,
                        DESCRIPTION TEXT NOT NULL,
                        LASTDAYTOCHANCEPRIZES DATETIME NOT NULL,
                        CAMPAIGNTYPE VARCHAR(20) NOT NULL,
                        REWARDTYPE VARCHAR(20) NOT NULL,
                        SCALETYPE VARCHAR(20) NOT NULL
                    );");

                statement.AppendLine($@"
                    CREATE TABLE IF NOT EXISTS {TABLE_SALE_CAMPAIGN_REWARDS} (
                        CUSTOMER VARCHAR({Customer.DATABASE_FIELD_IDENTIFIER_LENGHT}) NOT NULL,
		                LEVEL INT NOT NULL,
		                REWARDDATE DATETIME NOT NULL,
		                PROMOTION INT NOT NULL,
		                SEGMENT INT,
		                TITTLE VARCHAR(20) NOT NULL,
		                PRIZE DECIMAL(10, 2) NOT NULL,
		                STORE INT NOT NULL,
		                DOMAIN INT NULL
                    );
                ");

                string sql = statement.ToString();
                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (MySqlCommand command = new MySqlCommand(sql, connection))
                        {
                            command.ExecuteNonQuery();
                        }
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
            }

            internal override bool ExistCustomer(string accountNumber, int storeId)
            {
                string record = $@"
                    SELECT 1
                    FROM {TABLE_ACQUISITION} 
                    WHERE ACCOUNT = '{accountNumber}' AND STORE = {storeId};
                ";
                bool result = false;
                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (MySqlCommand command = new MySqlCommand(record, connection))
                        using (DbDataReader reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                result = true;
                            }
                            reader.Close();
                        }
                    }
                    catch
                    {
                        throw new GameEngineException("MySQL Error [" + record + "]");
                    }
                    finally
                    {
                        connection.Close();
                    }
                }

                return result;
            }

            internal override void StoreAcquisition(int storeId, string customerIdentifier, string accountNumber, DateTime acquisitionDate)
            {
                var date = ToDateString(acquisitionDate.Year, acquisitionDate.Month, acquisitionDate.Day, acquisitionDate.Hour, acquisitionDate.Minute, acquisitionDate.Second);
                string record = $@"
                    INSERT INTO {TABLE_ACQUISITION} (CUSTOMER, ACCOUNT, ACQUISITIONDATE, STORE)
                    VALUES
                    (
                        '{customerIdentifier}',
                        '{accountNumber}',
                        '{date}',
                        {storeId}
                    )
                    ON DUPLICATE KEY UPDATE
                        CUSTOMER = '{customerIdentifier}',
                        ACQUISITIONDATE = LEAST(ACQUISITIONDATE, VALUES(ACQUISITIONDATE));
                ";

                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (MySqlCommand command = new MySqlCommand(record, connection))
                        {
                            command.CommandType = CommandType.Text;
                            command.ExecuteNonQuery();
                        }
                    }
                    catch
                    {
                        throw new GameEngineException("MySQL Error [" + record + "]");
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
            }

            internal override void StoreRetention(int storeId, string customerIdentifier, string accountNumber, DateTime visitDate)
            {
                var date = ToDateString(visitDate.Year, visitDate.Month, visitDate.Day, visitDate.Hour, visitDate.Minute, visitDate.Second);
                string record = $@"
                    INSERT INTO {TABLE_VISITS} (CUSTOMER, ACCOUNT, LASTVISITOFDAY, VISITDATE, VISITCOUNTOFDAY, STORE)
                    VALUES
                    (
                        '{ customerIdentifier }',
                        '{ accountNumber }',
                        '{ date }',
                        date('{date}'),
                        '{ 1 }',
                        {storeId}
                    )
                    ON DUPLICATE KEY UPDATE
                        LASTVISITOFDAY = '{date}',
                        VISITCOUNTOFDAY = VISITCOUNTOFDAY+1
                ";

                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (MySqlCommand command = new MySqlCommand(record, connection))
                        {
                            command.CommandType = CommandType.Text;
                            command.ExecuteNonQuery();
                        }
                    }
                    catch
                    {
                        throw new GameEngineException("MySQL Error [" + record + "]");
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
            }

            internal override void StoreSale(int storeId, string customerIdentifier, string accountNumber, Currency amount, DateTime saleDate, int domainId)
            {
                var date = ToDateString(saleDate.Year, saleDate.Month, saleDate.Day, 0, 0, 0);
                string record = $@"
                    INSERT INTO {TABLE_SALES} (CUSTOMER, ACCOUNT, CURRENCY, AMOUNT, SALEDATE, STORE, DOMAIN)
                    VALUES
                    (
                        '{customerIdentifier}',
                        '{accountNumber}',
                        {amount.Coin.Id},
                        {amount.Value},
                        '{date}',
                        {storeId},
                        {domainId}
                    )
                    ON DUPLICATE KEY UPDATE
                        AMOUNT = AMOUNT+{amount.Value}
                ";

                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (MySqlCommand command = new MySqlCommand(record, connection))
                        {
                            command.CommandType = CommandType.Text;
                            command.ExecuteNonQuery();
                        }
                    }
                    catch
                    {
                        throw new GameEngineException("MySQL Error [" + record + "]");
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
            }

            internal override void StoreCurrency(CoinMessage msg)
            {
                string sql = $@"INSERT INTO currency(id, sign, isoCode, uniCode, decimalPrecision) VALUES (
					{msg.Coin.Id},
					'{msg.Coin.Sign}',
					'{msg.Coin.Iso4217Code}',
					'{msg.Coin.UnicodeAsText}',
					{msg.Coin.DecimalPrecision}
					);";

                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (MySqlCommand command = new MySqlCommand(sql, connection))
                        {
                            command.CommandType = CommandType.Text;
                            command.ExecuteNonQuery();
                        }
                    }
                    catch (Exception e)
                    {
                        throw new GameEngineException("MySQL Error [" + sql + "]");
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
            }

            internal override async Task<IEnumerable<AcquisitionDB>> FirstVisitForAsync()
            {
                string record = $@"
                    SELECT CUSTOMER, ACCOUNT, ACQUISITIONDATE, STORE
                    FROM {TABLE_ACQUISITION} 
                    ORDERBY CUSTOMER, ACCOUNT,ACQUISITIONDATE 
                    LIMIT 1;
                ";
                List<AcquisitionDB> result = new List<AcquisitionDB>();
                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        await connection.OpenAsync();
                        using (MySqlCommand command = new MySqlCommand(record, connection))
                        using (DbDataReader reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                AcquisitionDB row = new AcquisitionDB();
                                row.CustomerIdentifier = reader.GetString(0);
                                row.AccountNumber = reader.GetString(1);
                                row.AcquisitionDate = reader.GetDateTime(2);
                                row.StoreId = reader.GetInt32(3);
                                result.Add(row);
                            }
                            await reader.CloseAsync();
                        }
                    }
                    catch
                    {
                        throw new GameEngineException("MySQL Error [" + record + "]");
                    }
                    finally
                    {
                        await connection.CloseAsync();
                    }
                }

                return result;
            }

            private string ToDateString(int yyyy, int mm, int dd, int hh, int min, int s)
            {
                DateTime result = new DateTime(yyyy, mm, dd, hh, min, s);
                return result.ToString("yyyy-MM-dd HH:mm:ss");
            }

            internal override void StoreCampaign(int promotionNumber, DateTime creationDate, string name, string applicableCurrency, string returnedCurrency, decimal maxAmount, bool budgetIsUnlimited, DateTime startDateCampaign, DateTime endDateCampaign, int maxLevels, string descripcion, DateTime lastDayToChangePrizes, string campaignType, string rewardType, string scaleType)
            {
                var CREATIONDATE = ToDateString(creationDate.Year, creationDate.Month, creationDate.Day, creationDate.Hour, creationDate.Minute, creationDate.Second);
                var STARTDATECAMPAIGN = ToDateString(startDateCampaign.Year, startDateCampaign.Month, startDateCampaign.Day, startDateCampaign.Hour, startDateCampaign.Minute, startDateCampaign.Second);
                var ENDDATECAMPAIGN = ToDateString(endDateCampaign.Year, endDateCampaign.Month, endDateCampaign.Day, endDateCampaign.Hour, endDateCampaign.Minute, endDateCampaign.Second);
                var LASTDAYTOCHANCEPRIZES = ToDateString(lastDayToChangePrizes.Year, lastDayToChangePrizes.Month, lastDayToChangePrizes.Day, lastDayToChangePrizes.Hour, lastDayToChangePrizes.Minute, lastDayToChangePrizes.Second);
                var REWARDTYPE = rewardType.ToString();

                string AMOUNTVALUE = budgetIsUnlimited ? "NULL" : maxAmount.ToString();

                StringBuilder insertStatement = new StringBuilder();
                insertStatement.Append(
                    $@"INSERT INTO {TABLE_SALE_CAMPAIGNS}(PROMOTION, CREATEDDATE, NAME, APPLICABLECURRENCY, RETURNEDCURRENCY, MAXAMOUNT, STARTDATECAMPAIGN, ENDDATECAMPAIGN, MAXLEVELS, DESCRIPTION, LASTDAYTOCHANCEPRIZES, CAMPAIGNTYPE, REWARDTYPE, SCALETYPE) 
                    VALUES({promotionNumber}, '{CREATIONDATE}', '{name}', '{applicableCurrency}', '{returnedCurrency}', {AMOUNTVALUE}, '{STARTDATECAMPAIGN}', '{ENDDATECAMPAIGN}', {maxLevels}, '{descripcion}', '{LASTDAYTOCHANCEPRIZES}', '{campaignType}', '{REWARDTYPE}', '{scaleType}');"
                );

                string sql = insertStatement.ToString();
                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (MySqlCommand command = new MySqlCommand(sql, connection))
                        {
                            command.CommandType = CommandType.Text;
                            command.ExecuteNonQuery();
                        }
                    }
                    catch (Exception e)
                    {
                        throw new GameEngineException("MySQL Error [" + sql + "]");
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
            }

            internal override bool ExistStreakCamping(int promotionNumber)
            {
                string record = $"SELECT * FROM {TABLE_SALE_CAMPAIGNS} WHERE PROMOTION = {promotionNumber} LIMIT 1;";

                bool result = false;
                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (MySqlCommand command = new MySqlCommand(record, connection))
                        using (DbDataReader reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                result = true;
                            }
                            reader.Close();
                        }
                    }
                    catch
                    {
                        throw new GameEngineException("MySQL Error [" + record + "]");
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
                return result;
            }

            internal override void UpdateCampaign(int promotionNumber, string name, string applicableCurrency, string returnedCurrency, Budget budget, DateTime startDateCampaign, DateTime endDateCampaign, int maxLevels, string descripcion, DateTime lastDayToChangePrizes, string rewardType, string scaleType)
            {
                var result = ExistStreakCamping(promotionNumber);
                if (!result) throw new GameEngineException($"MySQL Error [{promotionNumber}] not Exist");

                string AMOUNTVALUE = budget.IsUnlimited() ? "NULL" : budget.MaxAmount.ToString();

                var STARTDATECAMPAIGN = ToDateString(startDateCampaign.Year, startDateCampaign.Month, startDateCampaign.Day, startDateCampaign.Hour, startDateCampaign.Minute, startDateCampaign.Second);
                var ENDDATECAMPAIGN = ToDateString(endDateCampaign.Year, endDateCampaign.Month, endDateCampaign.Day, endDateCampaign.Hour, endDateCampaign.Minute, endDateCampaign.Second);
                var LASTDAYTOCHANCEPRIZES = ToDateString(lastDayToChangePrizes.Year, lastDayToChangePrizes.Month, lastDayToChangePrizes.Day, lastDayToChangePrizes.Hour, lastDayToChangePrizes.Minute, lastDayToChangePrizes.Second);

                StringBuilder updateStatement = new StringBuilder();
                updateStatement.Append(
                    $@"UPDATE {TABLE_SALE_CAMPAIGNS} 
                    SET NAME = '{name}',
                    APPLICABLECURRENCY = '{applicableCurrency}',
                    RETURNEDCURRENCY = '{returnedCurrency}',
                    MAXAMOUNT = {AMOUNTVALUE},
                    STARTDATECAMPAIGN = '{STARTDATECAMPAIGN}',
                    ENDDATECAMPAIGN = '{ENDDATECAMPAIGN}',
                    MAXLEVELS = {maxLevels},
                    DESCRIPTION = '{descripcion}',
                    LASTDAYTOCHANCEPRIZES = '{LASTDAYTOCHANCEPRIZES}',
                    REWARDTYPE = '{rewardType}',
                    SCALETYPE = '{scaleType}'
                    WHERE PROMOTION = {promotionNumber};"
                );
                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (MySqlCommand command = new MySqlCommand(updateStatement.ToString(), connection))
                        {
                            command.CommandType = CommandType.Text;
                            command.ExecuteNonQuery();
                        }

                    }
                    catch
                    {
                        throw new GameEngineException("MySQL Error [" + updateStatement.ToString() + "]");
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
            }

            internal override void StoreCampaignReward(string playerIdentifier, int segment, string tittle, decimal prize, int level, DateTime now, int promotionNumber, int storeId, int domainId)
            {
                var date = ToDateString(now.Year, now.Month, now.Day, now.Hour, now.Minute, now.Second);
                string segmentValue = segment == -1 ? "NULL" : segment.ToString();
                StringBuilder insertStatement = new StringBuilder();
                insertStatement.Append(
                    $@"INSERT INTO {TABLE_SALE_CAMPAIGN_REWARDS}(CUSTOMER, LEVEL, REWARDDATE, PROMOTION, SEGMENT, TITTLE, PRIZE, STORE, DOMAIN) 
                    VALUES('{playerIdentifier}', {level}, '{date}', {promotionNumber}, {segmentValue}, '{tittle}', {prize}, {storeId}, {domainId});"
                );

                string sql = insertStatement.ToString();
                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (MySqlCommand command = new MySqlCommand(sql, connection))
                        {
                            command.CommandType = CommandType.Text;
                            command.ExecuteNonQuery();
                        }
                    }
                    catch (Exception e)
                    {
                        throw new GameEngineException("MySQL Error [" + sql + "]");
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
            }

            internal override DataTable RewardTableFromQuery(int page, int pageSize, string accountNumber, int promotionNumber, out int totalRows)
            {
                StringBuilder queryCount = new StringBuilder();
                queryCount.AppendLine($"SELECT COUNT(*) FROM {TABLE_SALE_CAMPAIGN_REWARDS}");

                StringBuilder query = new StringBuilder();
                query.AppendLine($"SELECT CUSTOMER, LEVEL, REWARDDATE, PROMOTION, SEGMENT, TITTLE, PRIZE, STORE, DOMAIN FROM {TABLE_SALE_CAMPAIGN_REWARDS}");

                List<string> parametersQuery = new List<string>()
                {
                    $"{(string.IsNullOrWhiteSpace(accountNumber) ? "":$"CUSTOMER = '{accountNumber}'")}",
                    $"{(promotionNumber == 0 ? "":$"PROMOTION = {promotionNumber}")}"
                };

                bool whereCondition = false;
                StringBuilder whereStatement = new StringBuilder();
                foreach (string parameter in parametersQuery)
                {
                    if (!string.IsNullOrWhiteSpace(parameter))
                    {
                        if (!whereCondition)
                        {
                            whereCondition = true;
                            whereStatement.Append($" WHERE {parameter}");
                        }
                        else
                        {
                            whereStatement.Append($" AND {parameter}");
                        }
                    }
                }
                whereStatement.AppendLine();

                queryCount.Append(whereStatement);
                query.Append(whereStatement);
                query.AppendLine($" ORDER BY CUSTOMER");
                query.AppendLine($"LIMIT {pageSize} OFFSET {(page - 1) * pageSize};");

                //execute query count query to get totalRows value
                totalRows = 0;
                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (MySqlCommand command = new MySqlCommand(queryCount.ToString(), connection))
                        using (DbDataReader reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                totalRows = reader.GetInt32(0);
                            }
                            reader.Close();
                        }
                    }
                    catch
                    {
                        throw new GameEngineException("MySQL Error [" + queryCount + "]");
                    }
                    finally
                    {
                        connection.Close();
                    }
                }

                //execute query to get data
                DataTable dataTable = new DataTable();
                try
                {
                    using (MySqlConnection connection = new MySqlConnection(connectionString))
                    {
                        connection.Open();
                        using (MySqlCommand command = new MySqlCommand(query.ToString(), connection))
                        {
                            using (MySqlDataReader reader = command.ExecuteReader())
                            {
                                dataTable.Load(reader);
                                return dataTable;
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error: {ex.Message}");
                    return null;
                }
            }

            internal override DataTable PlayerSalesTable(int page, int pageSize, string accountNumber, string currencyCode, DateTime startDate, DateTime endDate, IEnumerable<Store> storeIds, IEnumerable<Domain> domainIds, out int totalRows)
            {
                StringBuilder queryCount = new StringBuilder();
                queryCount.AppendLine($"SELECT COUNT(*) FROM {TABLE_SALES}");

                // create a string builder with a select with all the fields where the accountNumber mathes and the storeId, also make a pagination with the page and the pageSize usign OFFSET and LIMIT
                StringBuilder query = new StringBuilder();
                query.AppendLine($"SELECT CUSTOMER, ACCOUNT, CURRENCY, AMOUNT, SALEDATE, STORE, DOMAIN FROM {TABLE_SALES}");

                // add where query AppendLine
                List<string> parametersQuery = new List<string>()
                {
                    $"{(string.IsNullOrWhiteSpace(accountNumber) ? "":$"ACCOUNT = '{accountNumber}'")}",
                    $"{(string.IsNullOrWhiteSpace(currencyCode) ? "":$"CURRENCY = '{currencyCode}'")}",
                    $"{(startDate == DateTime.MinValue ? "":$"SALEDATE >= '{startDate.ToString("yyyy-MM-dd HH:mm:ss")}'")}",
                    $"{(endDate == DateTime.MinValue ? "":$"SALEDATE <= '{endDate.ToString("yyyy-MM-dd HH:mm:ss")}'")}",
                    $"{(!storeIds.Any() ? "":$"STORE IN ({string.Join(",", storeIds.Select(e => e.Id))})")}",
                    $"{(!domainIds.Any() ? "":$"DOMAIN IN ({string.Join(",", domainIds.Select(e => e.Id))})")}",
                };
                

                bool whereCondition = false;
                StringBuilder whereStatement = new StringBuilder();
                foreach (string parameter in parametersQuery)
                {
                    if (!string.IsNullOrWhiteSpace(parameter))
                    {
                        if (!whereCondition)
                        {
                            whereCondition = true;
                            whereStatement.Append($" WHERE {parameter}");
                        }
                        else
                        {
                            whereStatement.Append($" AND {parameter}");
                        }
                    }
                }
                whereStatement.AppendLine();

                queryCount.Append(whereStatement);
                query.Append(whereStatement);
                query.AppendLine($" ORDER BY CUSTOMER, SALEDATE");
                query.AppendLine($"LIMIT {pageSize} OFFSET {(page - 1) * pageSize};");

                //execute query count query to get totalRows value
                totalRows = 0;
                using (MySqlConnection connection = new MySqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (MySqlCommand command = new MySqlCommand(queryCount.ToString(), connection))
                        using (DbDataReader reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                totalRows = reader.GetInt32(0);
                            }
                            reader.Close();
                        }
                    }
                    catch
                    {
                        throw new GameEngineException("MySQL Error [" + queryCount + "]");
                    }
                    finally
                    {
                        connection.Close();
                    }
                }

                //execute query to get data
                DataTable dataTable = new DataTable();
                try
                {
                    using (MySqlConnection connection = new MySqlConnection(connectionString))
                    {
                        connection.Open();
                        using (MySqlCommand command = new MySqlCommand(query.ToString(), connection))
                        {
                            using (MySqlDataReader reader = command.ExecuteReader())
                            {
                                dataTable.Load(reader);
                                return dataTable;
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error: {ex.Message}");
                    return null;
                }
            }
        }

        private class LoyaltyStorageSQLServer : LoyaltyStorage
        {
            internal LoyaltyStorageSQLServer(string connectionString) : base(connectionString)
            {
                CrearStorage();
            }

            internal override bool ExistTable(string tableName)
            {
                bool exists = false;
                StringBuilder statement = new StringBuilder();

                statement.Append("IF EXISTS(")
                    .Append("SELECT 1 FROM INFORMATION_SCHEMA.TABLES ")
                    .Append("WHERE TABLE_NAME = '" + tableName + "')")
                    .Append("SELECT 1 ELSE SELECT 0;");
                string sql = statement.ToString();

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (SqlCommand command = new SqlCommand(sql, connection))
                        {
                            var result = (int)command.ExecuteScalar();
                            exists = result == 1;
                        }
                    }
                    catch
                    {
                        exists = false;
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
                return exists;
            }

            internal override bool ExistColumnTable(string tableName, string columnName)
            {
                StringBuilder statement = new StringBuilder();
                statement.Append("SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS ")
                    .Append("WHERE TABLE_NAME = '" + tableName + "' AND COLUMN_NAME = '" + columnName + "';");
                string sql = statement.ToString();
                bool exists = false;
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (SqlCommand command = new SqlCommand(sql, connection))
                        {
                            var result = (int)command.ExecuteScalar();
                            exists = result == 1;
                        }
                    }
                    catch
                    {
                        exists = false;
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
                return exists;
            }

            internal override void CrearStorage()
            {
                StringBuilder statement = new StringBuilder();

                if (!ExistTable(TABLE_ACQUISITION))
                {
                    statement.AppendLine($@"
				    CREATE TABLE IF NOT EXISTS currency(
					    id INT NOT NULL AUTO_INCREMENT,
					    sign VARCHAR(5) NOT NULL,
					    isoCode VARCHAR(5) NOT NULL,
					    uniCode VARCHAR(5) NOT NULL,
					    decimalPrecision TINYINT NOT NULL,
					    PRIMARY KEY(id));
			    ");
                }

                if (!ExistTable(TABLE_ACQUISITION))
                {
                    statement.AppendLine($@"
				    CREATE TABLE IF NOT EXISTS currency(
					    id INT NOT NULL AUTO_INCREMENT,
					    sign VARCHAR(5) NOT NULL,
					    isoCode VARCHAR(5) NOT NULL,
					    uniCode VARCHAR(5) NOT NULL,
					    decimalPrecision TINYINT NOT NULL,
					    PRIMARY KEY(id));
			    ");
                }

                if (!ExistTable(TABLE_ACQUISITION))
                {
                    statement
                    .Append("create table ").Append(TABLE_ACQUISITION)
                    .Append("(")
                    .Append($"CUSTOMER VARCHAR({Customer.DATABASE_FIELD_IDENTIFIER_LENGHT}) NOT NULL,")
                    .Append($"ACCOUNT VARCHAR({Customer.DATABASE_FIELD_ACCOUNT_LENGHT}) NOT NULL,")
                    .Append("ACQUISITIONDATE DATETIME NOT NULL,")
                    .Append("STORE TINYINT UNSIGNED NOT NULL,")
                    .Append($"CONSTRAINT {TABLE_ACQUISITION}_PK PRIMARY KEY (ACCOUNT, STORE)")
                    .Append(");");
                }

                if (!ExistTable(TABLE_VISITS))
                {
                    statement
                    .Append("create table ").Append(TABLE_VISITS)
                    .Append("(")
                    .Append($"CUSTOMER VARCHAR({Customer.DATABASE_FIELD_IDENTIFIER_LENGHT}) NOT NULL,")
                    .Append($"ACCOUNT VARCHAR({Customer.DATABASE_FIELD_ACCOUNT_LENGHT}) NOT NULL,")
                    .Append("LASTVISITOFDAY DATETIME NOT NULL,")
                    .Append("VISITCOUNTOFDAY INT NOT NULL,")
                    .Append("STORE TINYINT UNSIGNED NOT NULL")
                    .Append(");");
                    statement.Append($"CREATE INDEX {TABLE_VISITS}_IDX ON {TABLE_VISITS} (ACCOUNT, LASTVISITOFDAY);");
                }

                if (!ExistTable(TABLE_SALES))
                {
                    statement
                    .Append("create table ").Append(TABLE_SALES)
                    .Append("(")
                    .Append($"CUSTOMER VARCHAR({Customer.DATABASE_FIELD_IDENTIFIER_LENGHT}) NOT NULL,")
                    .Append($"ACCOUNT VARCHAR({Customer.DATABASE_FIELD_ACCOUNT_LENGHT}) NOT NULL,")
                    .Append("CURRENCY TINYINT NOT NULL,")
                    .Append("AMOUNT DECIMAL(16,8) NOT NULL,")
                    .Append("SALEDATE DATETIME NOT NULL,")
                    .Append("STORE TINYINT UNSIGNED NOT NULL,")
                    .Append("DOMAIN INT NOT NULL")
                    .Append(");");
                    statement.Append($"CREATE INDEX {TABLE_SALES}_IDX ON {TABLE_SALES} (ACCOUNT, SALEDATE, STORE, CURRENCY, DOMAIN);");
                }

                if (!ExistTable(TABLE_SALE_CAMPAIGN_REWARDS))
                {
                    statement
                    .Append("create table ").Append(TABLE_SALE_CAMPAIGN_REWARDS)
                    .Append("(")
                    .Append($"CUSTOMER VARCHAR({Customer.DATABASE_FIELD_IDENTIFIER_LENGHT}) NOT NULL,")
                    .Append("LEVEL INT NOT NULL,")
                    .Append("REWARDDATE DATETIME NOT NULL,")
                    .Append("PROMOTION INT NOT NULL,")
                    .Append("SEGMENT INT,")
                    .Append("TITTLE VARCHAR(20) NOT NULL,")
                    .Append("PRIZE INT NOT NULL,")
                    .Append("STORE INT NOT NULL,")
                    .Append("DOMAIN INT NOT NLL")
                    .Append(");");
                }

                if (!ExistTable(TABLE_SALE_CAMPAIGNS))
                {
                    statement
                    .Append("create table ").Append(TABLE_SALE_CAMPAIGNS)
                    .Append("(")
                    .Append("PROMOTION INT NOT NULL,")
                    .Append("CREATIONDATE DATETIME NOT NULL,")
                    .Append("NAME VARCHAR(20) NOT NULL,")
                    .Append("APPLICABLECURRENCY VARCHAR(5) NOT NULL,")
                    .Append("RETURNEDCURRENCY VARCHAR(5) NOT NULL,")
                    .Append("MAXAMOUNT DECIMAL(16, 8),")
                    .Append("STARTDATECAMPAIGN DATETIME NOT NULL,")
                    .Append("ENDDATECAMPAIGN DATETIME NOT NULL,")
                    .Append("MAXLEVELS INT NOT NULL,")
                    .Append("DESCRIPTION TEXT NOT NULL,")
                    .Append("LASTDAYTOCHANCEPRIZES DATETIME NOT NULL,")
                    .Append("CAMPAIGNTYPE VARCHAR(20) NOT NULL,")
                    .Append("REWARDTYPE VARCHAR(20) NOT NULL,")
                    .Append("SCALETYPE VARCHAR(20) NOT NULL")
                    .Append(");");
                }

                string sql = statement.ToString();
                if (string.IsNullOrEmpty(sql)) return;
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (SqlCommand command = new SqlCommand(sql, connection))
                        {
                            command.ExecuteNonQuery();
                        }
                    }
                    catch
                    {
                        throw new GameEngineException("SQLServer Error [" + sql + "]");
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
            }

            internal override bool ExistCustomer(string accountNumber, int storeId)
            {
                string record = $@"
                    SELECT 1
                    FROM {TABLE_ACQUISITION} 
                    WHERE ACCOUNT = '{accountNumber}' AND STORE = {storeId};
                ";
                bool result = false;
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (SqlCommand command = new SqlCommand(record, connection))
                        using (DbDataReader reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                result = true;
                            }
                            reader.Close();
                        }
                    }
                    catch
                    {
                        throw new GameEngineException("SQLServer Error [" + record + "]");
                    }
                    finally
                    {
                        connection.Close();
                    }
                }

                return result;
            }

            internal override void StoreAcquisition(int storeId, string customerIdentifier, string accountNumber, DateTime acquisitionDate)
            {
                var date = ToDateString(acquisitionDate.Year, acquisitionDate.Month, acquisitionDate.Day, acquisitionDate.Hour, acquisitionDate.Minute, acquisitionDate.Second);

                string record = $@"
                    IF EXISTS (
                        SELECT 1 FROM {TABLE_ACQUISITION} WHERE ACCOUNT = '{accountNumber}' AND STORE = {storeId}
                    )
                    BEGIN
                        UPDATE {TABLE_ACQUISITION}
                        SET 
                            CUSTOMER = '{customerIdentifier}',
                            ACQUISITIONDATE = 
                                CASE 
                                    WHEN ACQUISITIONDATE < '{date}' THEN ACQUISITIONDATE
                                    ELSE '{date}'
                                END
                        WHERE ACCOUNT = '{accountNumber}' AND STORE = {storeId}
                    END
                    ELSE
                    BEGIN
                        INSERT INTO {TABLE_ACQUISITION} (CUSTOMER, ACCOUNT, ACQUISITIONDATE, STORE)
                        VALUES ('{customerIdentifier}', '{accountNumber}', '{date}', {storeId})
                    END
                ";

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (SqlCommand command = new SqlCommand(record, connection))
                        {
                            command.CommandType = CommandType.Text;
                            command.ExecuteNonQuery();
                        }
                    }
                    catch
                    {
                        throw new GameEngineException("SQLServer Error [" + record + "]");
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
            }

            internal override void StoreRetention(int storeId, string customerIdentifier, string accountNumber, DateTime visitDate)
            {
                var date = ToDateString(visitDate.Year, visitDate.Month, visitDate.Day, visitDate.Hour, visitDate.Minute, visitDate.Second);

                string record = $@"
                    IF NOT EXISTS (
                        SELECT * 
                        FROM {TABLE_VISITS} 
                        WHERE ACCOUNT = '{ accountNumber }' AND LASTVISITOFDAY = '{date}' AND 
                            STORE = {storeId}
                    ) BEGIN
                        INSERT INTO {TABLE_VISITS} (CUSTOMER, ACCOUNT, LASTVISITOFDAY, VISITCOUNTOFDAY, STORE)
                        VALUES
                        (
                            '{ customerIdentifier }',
                            '{ accountNumber }',
                            '{ date }',
                            '{ 1 }',
                            {storeId}
                        )
                    END
                    ELSE BEGIN
                        UPDATE {TABLE_VISITS} 
                        SET LASTVISITOFDAY = '{ date }',
                            VISITCOUNTOFDAY = VISITCOUNTOFDAY+1
                        WHERE ACCOUNT = '{ accountNumber }' AND LASTVISITOFDAY = '{date}' AND 
                            STORE = {storeId}
                    END
                ";

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (SqlCommand command = new SqlCommand(record, connection))
                        {
                            command.CommandType = CommandType.Text;
                            command.ExecuteNonQuery();
                        }
                    }
                    catch
                    {
                        throw new GameEngineException("SQLServer Error [" + record + "]");
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
            }

            internal override void StoreSale(int storeId, string customerIdentifier, string accountNumber, Currency amount, DateTime saleDate, int domainId)
            {
                var date = ToDateString(saleDate.Year, saleDate.Month, saleDate.Day, 0, 0, 0);

                string record = $@"
                    IF NOT EXISTS (
                        SELECT * 
                        FROM {TABLE_SALES} 
                        WHERE ACCOUNT = '{accountNumber}' AND SALEDATE = '{date}' AND 
                            STORE = {storeId} AND CURRENCY = '{amount.CurrencyCodeAsText}' AND DOMAIN = {domainId}
                    ) BEGIN
                        INSERT INTO {TABLE_SALES} (CUSTOMER, ACCOUNT, CURRENCYID, AMOUNT, SALEDATE, STORE, DOMAIN)
                        VALUES
                        (
                            '{customerIdentifier}',
                            '{accountNumber}',
                            {amount.Coin.Id},
                            {amount.Value},
                            '{date}',
                            {storeId},
                            {domainId}
                        )
                    END
                    ELSE BEGIN
                        UPDATE {TABLE_SALES} 
                        SET AMOUNT = AMOUNT+{amount.Value}
                        WHERE ACCOUNT = '{accountNumber}' AND SALEDATE = '{date}' AND STORE = {storeId} AND CURRENCY = {amount.Coin.Id} AND DOMAIN = {domainId}
                    END
                ";

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (SqlCommand command = new SqlCommand(record, connection))
                        {
                            command.CommandType = CommandType.Text;
                            command.ExecuteNonQuery();
                        }
                    }
                    catch
                    {
                        throw new GameEngineException("SQLServer Error [" + record + "]");
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
            }

            internal override void StoreCurrency(CoinMessage msg)
            {
                string sql = $@"INSERT INTO currency(id, sign, isoCode, uniCode, decimalPrecision) VALUES (
					{msg.Coin.Id},
					'{msg.Coin.Sign}',
					'{msg.Coin.Iso4217Code}',
					'{msg.Coin.UnicodeAsText}',
					{msg.Coin.DecimalPrecision}
					);";

                using (var connection = new SqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (SqlCommand command = new SqlCommand(sql, connection))
                        {
                            command.CommandType = CommandType.Text;
                            command.ExecuteNonQuery();
                        }
                    }
                    catch (Exception e)
                    {
                        throw new GameEngineException("SQLServer Error [" + sql + "]");
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
            }

            private string ToDateString(int yyyy, int mm, int dd, int hh, int min, int s)
            {
                DateTime result = new DateTime(yyyy, mm, dd, hh, min, s);
                return result.ToString("yyyy-MM-dd HH:mm:ss");
            }

            internal override Task<IEnumerable<AcquisitionDB>> FirstVisitForAsync()
            {
                throw new NotImplementedException();
            }

            internal override void StoreCampaign(int promotionNumber, DateTime creationDate, string name, string applicableCurrency, string returnedCurrency, decimal maxAmount, bool budgetIsUnlimited, DateTime startDateCampaign, DateTime endDateCampaign, int maxLevels, string descripcion, DateTime lastDayToChangePrizes, string campaignType, string rewardType, string scaleType)
            {
                var CREATIONDATE = ToDateString(creationDate.Year, creationDate.Month, creationDate.Day, creationDate.Hour, creationDate.Minute, creationDate.Second);
                var STARTDATECAMPAIGN = ToDateString(startDateCampaign.Year, startDateCampaign.Month, startDateCampaign.Day, startDateCampaign.Hour, startDateCampaign.Minute, startDateCampaign.Second);
                var ENDDATECAMPAIGN = ToDateString(endDateCampaign.Year, endDateCampaign.Month, endDateCampaign.Day, endDateCampaign.Hour, endDateCampaign.Minute, endDateCampaign.Second);
                var LASTDAYTOCHANCEPRIZES = ToDateString(lastDayToChangePrizes.Year, lastDayToChangePrizes.Month, lastDayToChangePrizes.Day, lastDayToChangePrizes.Hour, lastDayToChangePrizes.Minute, lastDayToChangePrizes.Second);
                var REWARDTYPE = rewardType.ToString();

                string AMOUNTVALUE = budgetIsUnlimited ? "NULL" : maxAmount.ToString();

                StringBuilder insertStatement = new StringBuilder();
                insertStatement.Append($@"
                    INSERT INTO {TABLE_SALE_CAMPAIGNS}(PROMOTION, CREATIONDATE, NAME, APPLICABLECURRENCY, RETURNEDCURRENCY, MAXAMOUNT, STARTDATECAMPAIGN, ENDDATECAMPAIGN, MAXLEVELS, DESCRIPTION, LASTDAYTOCHANCEPRIZES, CAMPAIGNTYPE, REWARDTYPE, SCALETYPE) 
                    VALUES({promotionNumber}, '{CREATIONDATE}', '{name}', '{applicableCurrency}', '{returnedCurrency}', {AMOUNTVALUE}, '{STARTDATECAMPAIGN}', '{ENDDATECAMPAIGN}', {maxLevels}, '{descripcion}', '{LASTDAYTOCHANCEPRIZES}', '{campaignType}', '{REWARDTYPE}', '{scaleType}');"
                );
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (SqlCommand command = new SqlCommand(insertStatement.ToString(), connection))
                        {
                            command.CommandType = CommandType.Text;
                            command.ExecuteNonQuery();
                        }
                    }
                    catch
                    {
                        throw new GameEngineException("SQLServer Error [" + insertStatement.ToString() + "]");
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
            }

            internal override bool ExistStreakCamping(int promotionNumber)
            {
                string record = $@"
                    SELECT 1
                    FROM {TABLE_SALE_CAMPAIGNS} 
                    WHERE PROMOTION = '{promotionNumber};
                ";
                bool result = false;
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (SqlCommand command = new SqlCommand(record, connection))
                        using (DbDataReader reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                result = true;
                            }
                            reader.Close();
                        }
                    }
                    catch
                    {
                        throw new GameEngineException("SQLServer Error [" + record + "]");
                    }
                    finally
                    {
                        connection.Close();
                    }
                }

                return result;
            }

            internal override void UpdateCampaign(int promotionNumber, string name, string applicableCurrency, string returnedCurrency, Budget budget, DateTime startDateCampaign, DateTime endDateCampaign, int maxLevels, string descripcion, DateTime lastDayToChangePrizes, string rewardType, string scaleType)
            {
                var result = ExistStreakCamping(promotionNumber);
                if (!result) throw new GameEngineException($"MySQL Error [{promotionNumber}] not Exist");

                string AMOUNTVALUE = budget.IsUnlimited() ? "NULL" : budget.MaxAmount.ToString();

                StringBuilder updateStatement = new StringBuilder();
                updateStatement.Append($@"
                    UPDATE {TABLE_SALE_CAMPAIGNS} 
                    SET NAME = '{name}',
                    APPLICABLECURRENCY = '{applicableCurrency}',
                    RETURNEDCURRENCY = '{returnedCurrency}',
                    MAXAMOUNT = {AMOUNTVALUE},
                    STARTDATECAMPAIGN = '{startDateCampaign}',
                    ENDDATECAMPAIGN = '{endDateCampaign}',
                    MAXLEVELS = {maxLevels},
                    DESCRIPTION = '{descripcion}',
                    LASTDAYTOCHANCEPRIZES = '{lastDayToChangePrizes}',
                    REWARDTYPE = '{rewardType}',
                    SCALETYPE = '{scaleType}'
                    WHERE PROMOTION = {promotionNumber};"
                );

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (SqlCommand command = new SqlCommand(updateStatement.ToString(), connection))
                        {
                            command.CommandType = CommandType.Text;
                            command.ExecuteNonQuery();
                        }

                    }
                    catch
                    {
                        throw new GameEngineException("SQLServer Error [" + updateStatement.ToString() + "]");
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
            }

            internal override void StoreCampaignReward(string playerIdentifier, int segment, string tittle, decimal prize, int level, DateTime now, int promotionNumber, int storeId, int domainId)
            {
                var date = ToDateString(now.Year, now.Month, now.Day, now.Hour, now.Minute, now.Second);
                string segmentValue = segment == -1 ? "NULL" : segment.ToString();
                string record = $@"
                INSERT INTO {TABLE_SALE_CAMPAIGN_REWARDS} (CUSTOMER, LEVEL, REWARDDATE, PROMOTION, SEGMENT, TITTLE, PRIZE, STORE, DOMAIN) 
                    VALUES('{playerIdentifier}', {level}, '{date}', {promotionNumber}, {segmentValue}, '{tittle}', {prize}, {storeId}, {domainId});
                ";

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (SqlCommand command = new SqlCommand(record, connection))
                        {
                            command.CommandType = CommandType.Text;
                            command.ExecuteNonQuery();
                        }
                    }
                    catch
                    {
                        throw new GameEngineException("SQLServer Error [" + record + "]");
                    }
                    finally
                    {
                        connection.Close();
                    }
                }
            }

            internal override DataTable RewardTableFromQuery(int page, int pageSize, string accountNumber, int promotionNumber, out int totalRows)
            {
                StringBuilder queryCount = new StringBuilder();
                queryCount.AppendLine($"SELECT COUNT(*) FROM {TABLE_SALE_CAMPAIGN_REWARDS}");

                StringBuilder query = new StringBuilder();
                query.AppendLine($"SELECT CUSTOMER, LEVEL, REWARDDATE, PROMOTION, SEGMENT, TITTLE, PRIZE, STORE, DOMAIN FROM {TABLE_SALE_CAMPAIGN_REWARDS}");

                List<string> parametersQuery = new List<string>()
                {
                    $"{(string.IsNullOrWhiteSpace(accountNumber) ? "":$"CUSTOMER = '{accountNumber}'")}",
                    $"{(promotionNumber == 0 ? "":$"PROMOTION = {promotionNumber}")}"
                };

                bool whereCondition = false;
                StringBuilder whereStatement = new StringBuilder();
                foreach (string parameter in parametersQuery)
                {
                    if (!string.IsNullOrWhiteSpace(parameter))
                    {
                        if (!whereCondition)
                        {
                            whereCondition = true;
                            whereStatement.Append($" WHERE {parameter}");
                        }
                        else
                        {
                            whereStatement.Append($" AND {parameter}");
                        }
                    }
                }
                whereStatement.AppendLine();

                query.Append(whereStatement);
                queryCount.Append(whereStatement);
                query.AppendLine($"ORDER BY CUSTOMER");
                query.AppendLine($"OFFSET {(page - 1) * pageSize} * {pageSize} ROWS");
                query.AppendLine($"FETCH NEXT {pageSize} ROWS ONLY;");

                //execute query count query to get totalRows value
                totalRows = 0;
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (SqlCommand command = new SqlCommand(queryCount.ToString(), connection))
                        {
                            totalRows = Convert.ToInt32(command.ExecuteScalar());
                        }
                    }
                    catch
                    {
                        throw new GameEngineException("SQLServer Error [" + queryCount.ToString() + "]");
                    }
                    finally
                    {
                        connection.Close();
                    }
                }

                DataTable dataTable = new DataTable();
                using (SqlConnection conexion = new SqlConnection(connectionString))
                {
                    conexion.Open();
                    using (SqlCommand comando = new SqlCommand(query.ToString(), conexion))
                    {
                        using (SqlDataAdapter adaptador = new SqlDataAdapter(comando))
                        {
                            adaptador.Fill(dataTable);
                        }
                    }
                }

                return dataTable;
            }

            internal override DataTable PlayerSalesTable(int page, int pageSize, string accountNumber, string currencyCode, DateTime startDate, DateTime endDate, IEnumerable<Store> storeIds, IEnumerable<Domain> domainIds, out int totalRows)
            {
                StringBuilder queryCount = new StringBuilder();
                queryCount.AppendLine($"SELECT COUNT(*) FROM {TABLE_SALES}");
                // create a string builder with a select with all the fields where the accountNumber mathes and the storeId, also make a pagination with the page and the pageSize usign OFFSET and LIMIT
                StringBuilder query = new StringBuilder();
                query.AppendLine($"SELECT CUSTOMER, ACCOUNT, CURRENCY, AMOUNT, SALEDATE, STORE, DOMAIN FROM {TABLE_SALES}");
                // add where query AppendLine
                List<string> parametersQuery = new List<string>()
                {
                    $"{(string.IsNullOrWhiteSpace(accountNumber) ? "":$"ACCOUNT = '{accountNumber}'")}",
                    $"{(string.IsNullOrWhiteSpace(currencyCode) ? "":$"CURRENCY = '{currencyCode}'")}",
                    $"{(startDate == DateTime.MinValue ? "":$"SALEDATE >= '{startDate.ToString("yyyy-MM-dd HH:mm:ss")}'")}",
                    $"{(endDate == DateTime.MinValue ? "":$"SALEDATE <= '{endDate.ToString("yyyy-MM-dd HH:mm:ss")}'")}",
                    $"{(!storeIds.Any() ? "":$"STORE IN ({string.Join(",", storeIds.Select(e => e.Id))})")}",
                    $"{(!domainIds.Any() ? "":$"DOMAIN IN ({string.Join(",", domainIds.Select(e => e.Id))})")}",
                };
                bool whereCondition = false;
                StringBuilder whereStatement = new StringBuilder();
                foreach (string parameter in parametersQuery)
                {
                    if (!string.IsNullOrWhiteSpace(parameter))
                    {
                        if (!whereCondition)
                        {
                            whereCondition = true;
                            whereStatement.Append($" WHERE {parameter}");
                        }
                        else
                        {
                            whereStatement.Append($" AND {parameter}");
                        }
                    }
                }
                whereStatement.AppendLine();
                queryCount.Append(whereStatement);
                query.Append(whereStatement);
                query.AppendLine($" ORDER BY CUSTOMER, SALEDATE");
                query.AppendLine($"OFFSET {(page - 1) * pageSize} * {pageSize} ROWS");
                query.AppendLine($"FETCH NEXT {pageSize} ROWS ONLY;");
                //execute query count query to get totalRows value
                totalRows = 0;
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    try
                    {
                        connection.Open();
                        using (SqlCommand command = new SqlCommand(queryCount.ToString(), connection))
                        {
                            totalRows = Convert.ToInt32(command.ExecuteScalar());
                        }
                    }
                    catch
                    {
                        throw new GameEngineException("SQLServer Error [" + queryCount.ToString() + "]");
                    }
                    finally
                    {
                        connection.Close();
                    }
                }

                DataTable dataTable = new DataTable();
                using (SqlConnection conexion = new SqlConnection(connectionString))
                {
                    conexion.Open();
                    using (SqlCommand comando = new SqlCommand(query.ToString(), conexion))
                    {
                        using (SqlDataAdapter adaptador = new SqlDataAdapter(comando))
                        {
                            adaptador.Fill(dataTable);
                        }
                    }
                }

                return dataTable;
            }
        }

        internal void StoreCampaignReward(string playerIdentifier, int segment, string tittle, decimal prize, int level, DateTime now, int promotionNumber, int storeId, int domainId)
        {
            if (loyaltyStorage == null) throw new GameEngineException($"The {nameof(loyaltyStorage)} has not been set properly. Please check the contructor {nameof(MarketingStorage)}.");
            loyaltyStorage.StoreCampaignReward(playerIdentifier, segment, tittle, prize, level, now, promotionNumber, storeId, domainId);
        }

        internal void StoreCampaign(int promotionNumber, DateTime creationDate, string name, string applicableCurrency, string returnedCurrency, decimal maxAmount, bool budgetIsUnlimited, DateTime startDateCampaign, DateTime endDateCampaign, int maxLevels, string descripcion, DateTime lastDayToChangePrizes, string campaignType, string rewardType, string scaleType)
        {
            if (loyaltyStorage == null) throw new GameEngineException($"The {nameof(loyaltyStorage)} has not been set properly. Please check the contructor {nameof(MarketingStorage)}.");
            loyaltyStorage.StoreCampaign(promotionNumber, creationDate, name, applicableCurrency, returnedCurrency, maxAmount, budgetIsUnlimited, startDateCampaign, endDateCampaign, maxLevels, descripcion, lastDayToChangePrizes, campaignType, rewardType, scaleType);
        }

        internal void UpdateCampaign(int promotionNumber, string name, string applicableCurrency, string returnedCurrency, Budget budget, DateTime startDateCampaign, DateTime endDateCampaign, int maxLevels, string descripcion, DateTime lastDayToChangePrizes, string rewardType, string scaleType)
        {
            if (loyaltyStorage == null) throw new GameEngineException($"The {nameof(loyaltyStorage)} has not been set properly. Please check the contructor {nameof(MarketingStorage)}.");
            loyaltyStorage.UpdateCampaign(promotionNumber, name, applicableCurrency, returnedCurrency, budget, startDateCampaign, endDateCampaign, maxLevels, descripcion, lastDayToChangePrizes, rewardType, scaleType);
        }

        private ResponseRewardDataTable responseRewardData = new ResponseRewardDataTable();
        internal ResponseRewardDataTable SaleCampaignRewardJsonResult(int page, int pageSize, string accountNumber, int promotionNumber, string domainIdsOrAll)
        {
            if (loyaltyStorage == null) throw new GameEngineException($"The {nameof(loyaltyStorage)} has not been set properly. Please check the contructor {nameof(MarketingStorage)}.");
            int totalRows = 0;
            var table = loyaltyStorage.RewardTableFromQuery(page, pageSize, accountNumber, promotionNumber, out totalRows);
            responseRewardData.TotalRows = totalRows;
            responseRewardData.ReportData.Clear();
            foreach (DataRow row in table.Rows)
            {
                RowRewardDataTable rowData = new RowRewardDataTable(
                    Convert.ToString(row["CUSTOMER"]),
                    Convert.ToInt32(row["LEVEL"]),
                    Convert.ToDateTime(row["REWARDDATE"]),
                    Convert.ToInt32(row["PROMOTION"]),
                    Convert.ToInt32(row["SEGMENT"] != DBNull.Value ? row["SEGMENT"] : 0),
                    Convert.ToString(row["TITTLE"]),
                    Convert.ToDecimal(row["PRIZE"]),
                    Convert.ToInt32(row["STORE"]),
                    Convert.ToInt32(row["DOMAIN"])
                );
                responseRewardData.ReportData.Add(rowData);
            }

            var allDomains = domainIdsOrAll == Reports.SELECTION_ALL;
            if (!allDomains)
            {
                var domainIds = domainIdsOrAll.Split(',');
                responseRewardData.FilterDomains(domainIds);
            }

            return responseRewardData;
        }

        private ResponseSaleDataTable responseSaleDataTable = new ResponseSaleDataTable();
        internal ResponseSaleDataTable SalesByPlayer(string accountNumber, CampaignBase saleCampaign, int page, int pageSize)
        {
            if (loyaltyStorage == null) throw new GameEngineException($"The {nameof(loyaltyStorage)} has not been set properly. Please check the contructor {nameof(MarketingStorage)}.");
            int totalRows = 0;
            var table = loyaltyStorage.PlayerSalesTable(page, pageSize, accountNumber, saleCampaign.AllowedCurrency, saleCampaign.StartDate, saleCampaign.EndDate, saleCampaign.AllowedStores, saleCampaign.AllowedDomains, out totalRows);
            responseSaleDataTable.TotalRows = totalRows;
            responseSaleDataTable.ReportData.Clear();
            foreach (DataRow row in table.Rows)
            {
                RowPlayerSaleDataTable rowData = new RowPlayerSaleDataTable(
                    Convert.ToString(row["CUSTOMER"]),
                    Convert.ToString(row["ACCOUNT"]),
                    Convert.ToString(row["CURRENCY"]),
                    Convert.ToDecimal(row["AMOUNT"]),
                    Convert.ToDateTime(row["SALEDATE"]),
                    Convert.ToInt32(row["STORE"]),
                    Convert.ToInt32(row["DOMAIN"])
                );
                responseSaleDataTable.ReportData.Add(rowData);
            }
            return responseSaleDataTable;
        }

        [Puppet]
        internal abstract class ResponseDataTable<T> : Objeto
        {
            internal int TotalRows { get; set; }
            internal List<T> ReportData { get; set; }

            internal ResponseDataTable()
            {
                TotalRows = 0;
                ReportData = new List<T>();
            }

            internal int TotalRecords
            {
                get
                {
                    return ReportData.Count;
                }
            }

            internal abstract void FilterDomains(string[] domainIds);
        }

        [Puppet]
        internal class ResponseRewardDataTable : ResponseDataTable<RowRewardDataTable>
        {
            internal override void FilterDomains(string[] domainIds)
            {
                foreach (var domainId in domainIds)
                {
                    int totalRemoved = ReportData.RemoveAll(x => x.Domain != Convert.ToInt32(domainId));
                    TotalRows -= totalRemoved;
                }
            }
        }

        internal class ResponseSaleDataTable : ResponseDataTable<RowPlayerSaleDataTable>
        {
            internal override void FilterDomains(string[] domainIds)
            {
                foreach (var domainId in domainIds)
                {
                    int totalRemoved = ReportData.RemoveAll(x => x.Domain != Convert.ToInt32(domainId));
                    TotalRows -= totalRemoved;
                }
            }
        }

        [Puppet]
        internal class RowRewardDataTable : Objeto
        {
            internal string Customer { get; set; }
            internal int Level { get; set; }
            internal DateTime RewardDate { get; set; }
            internal int Promotion { get; set; }
            internal int Segment { get; set; }
            internal string Tittle { get; set; }
            internal decimal Prize { get; set; }
            internal int Store { get; set; }
            internal int Domain { get; set; }

            internal RowRewardDataTable(string customer, int level, DateTime rewardDate, int promotion, int segmentIndex, string tittle, decimal prize, int store, int domain)
            {
                Customer = customer;
                Level = level;
                RewardDate = rewardDate;
                Promotion = promotion;
                Segment = segmentIndex;
                Tittle = tittle;
                Prize = prize;
                Store = store;
                Domain = domain;
            }
        }

        [Puppet]
        internal class RowPlayerSaleDataTable : Objeto
        {
            internal string Customer { get; set; }
            internal string Account { get; set; }
            internal string Currency { get; set; }
            internal decimal Amount { get; set; }
            internal DateTime SaleDate { get; set; }
            internal int Store { get; set; }
            internal int Domain { get; set; }

            internal RowPlayerSaleDataTable(string customer, string account, string currency, decimal amount, DateTime saleDate, int store, int domain)
            {
                Customer = customer;
                Account = account;
                Currency = currency;
                Amount = amount;
                SaleDate = saleDate;
                Store = store;
                Domain = domain;
            }
        }
    }
}
 
 
 
 