﻿
using Connectors.town.connectors.driver.transactions;
using GamesEngine;
using GamesEngine.Business;
using GamesEngine.Business.Liquidity;
using GamesEngine.Business.Liquidity.Containers;
using GamesEngine.Business.Liquidity.Sentinels.Inbound;
using GamesEngine.Settings;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System.Text;
using town.connectors.drivers;
using town.connectors.drivers.hades;
using static GamesEngine.Business.Liquidity.Containers.Dispenser;
using static GamesEngine.Business.Liquidity.Containers.Dispenser.DispenserReady;
using static GamesEngine.Business.Liquidity.Containers.Tanker;
using static GamesEngine.Business.Liquidity.Containers.Tanker.TankerDispatched;
using static GamesEngine.Business.Liquidity.Sentinels.Inbound.RateInboundTask;
using static GamesEngine.Business.WholePaymentProcessor;
using static GamesEngine.Exchange.town.connectors.drivers.fierro.processors.UnlockDeposit;
using static GamesEngine.Settings.PaymentManager.PaymentInvoice;
using static LiquidityAPI.Controllers.APIController;
using static town.connectors.CustomSettings;

namespace LiquidityAPI.Controllers
{
    internal class ConsumerMessaging
    {
        internal void CreateConsumerForTopics()
        {
            new DispatchTankerConsumer(Integration.Kafka.Group, Integration.Kafka.TopicForLiquidityEvents).StartListening();
        }

        internal class DispatchTankerConsumer : GamesEngine.Settings.Consumer
        {
            internal string Topic { get; private set; }

            internal DispatchTankerConsumer(string group, string topic) : base(group, topic)
            {
                Topic = topic;
            }

            public override void OnMessageBeforeCommit(string msg)
            {
                try
                {
                    if (string.IsNullOrWhiteSpace(msg)) throw new ArgumentNullException(nameof(msg));
                    LiquidityMessageType messageType = LiquidityEventMessage.GetType(msg);
                    switch (messageType)
                    {
                        case LiquidityMessageType.SentinelConfirmDeposit:
                            ConfirmedDepositDueBody confirmedDepositDueBody = new ConfirmedDepositDueBody(msg);
                            ConfirmDeposit(confirmedDepositDueBody);
                            break;
                        case LiquidityMessageType.TankerDispatched:
                            var dispatchTankerMessage = new DispatchTankerMessage(msg);
                            if (dispatchTankerMessage.TankerId <= 0) throw new ArgumentNullException(nameof(dispatchTankerMessage.TankerId));

                            var result = LiquidityAPI.Liquidity.PerformChkThenQry(@$"
                            {{
                                existKind = company.System.LiquidFlow.ExistInstance('{dispatchTankerMessage.Kind}');
				                Check(existKind) Error 'The request kind:{dispatchTankerMessage.Kind} does not exist.';

                                if (existKind)
                                {{
                                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{dispatchTankerMessage.Kind}');
                                    existTanker = liquid.Source.ExistTanker({dispatchTankerMessage.TankerId});
                                    Check(existTanker) Error 'The request tanker id:{dispatchTankerMessage.TankerId} does not exist.';
                                    if (existTanker)
                                    {{
                                        tankerSealed = liquid.Source.FindTanker({dispatchTankerMessage.TankerId});
                                        validSealedType = tankerSealed.Type == '{typeof(TankerSealed).Name}';
                                        Check(validSealedType) Error 'The request tanker id:{dispatchTankerMessage.TankerId} is not a sealed tanker.' + tankerSealed.Type;
                                    }}
                                }}
                            }}
                            ", @$"
                            {{
                                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{dispatchTankerMessage.Kind}');
                                tankerSealed = liquid.Source.FindTanker({dispatchTankerMessage.TankerId});
                        
                                tankerTask = liquid.IngressSentinel.FindTask(tankerSealed);
                                for (tankerTaskDeposits : tankerTask.DepositChainStatuses)
                                {{
                                    tankerDeposit = tankerTaskDeposits;
                                    print tankerDeposit.Id id;
                                    print tankerDeposit.Available available;
                                }}
                            }}
                            ");
                            if (!(result is OkObjectResult)) throw new Exception($"Failed to process tanker with ID {dispatchTankerMessage.TankerId}. Result: {result}");

                            string jsonResult = (result as ObjectResult).Value.ToString();
                            var options = new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                            var tankerDepositsDTO = System.Text.Json.JsonSerializer.Deserialize<TankerDepositsDTO>(jsonResult, options);

                            bool tankerCanBeDipatch = tankerDepositsDTO.TankerTaskDeposits.All(d => d.Available == 0);
                            if (tankerCanBeDipatch)
                            {
                                result = LiquidityAPI.Liquidity.PerformCmd(@$"
                                {{
                                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{dispatchTankerMessage.Kind}');
                                    tankerSealed = liquid.Source.FindTanker({dispatchTankerMessage.TankerId});
                                    tankerSealed.Dispatched(now);
                                }}
                                ");
                                if (!(result is OkObjectResult)) throw new Exception($"Failed to dispatch tanker with ID {dispatchTankerMessage.TankerId}.");
                            }
                            else
                            {
                                var strDepositIds = string.Join(",", tankerDepositsDTO.TankerTaskDeposits.Where(d => d.Available == 0M).Select(d => d.Id));
                                result = LiquidityAPI.Liquidity.PerformCmd(@$"
                                {{
                                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{dispatchTankerMessage.Kind}');
                                    tankerSealed = liquid.Source.FindTanker({dispatchTankerMessage.TankerId});
                                    Eval('partialTankerId = '+liquid.Source.NextTankerId()+';');
                                    tankerSealed.PartialDispatched(partialTankerId, now, {{{strDepositIds}}});
                                }}
                                ");
                                if (!(result is OkObjectResult)) throw new Exception($"Failed to partially dispatch tanker with ID {dispatchTankerMessage.TankerId}.");
                            }                        
                            break;
                        case LiquidityMessageType.DispatchedDispenser:
                            var dispatchedDispenser = new DispatchedDispenserMessage(msg);

                            string dispatchedWithdrawlsComas = string.Join(",", dispatchedDispenser.DispathcedWithdrawls);

                            var resultWithdraws = LiquidityAPI.Liquidity.PerformChkThenCmd(@$"
                            {{
                                existKind = company.System.LiquidFlow.ExistInstance('{dispatchedDispenser.Kind}');  
                                Check(existKind) Error 'The request kind:{dispatchedDispenser.Kind} does not exist.';
                                if (existKind)
                                {{
                                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{dispatchedDispenser.Kind}');
                                    existDispenser = liquid.Outlet.ExistDispenser({dispatchedDispenser.DispenserId});
                                    Check(existDispenser) Error 'The request dispenser id:{dispatchedDispenser.DispenserId} does not exist.';
                                    if (existDispenser)
                                    {{
                                        dispenserReady = liquid.Outlet.FindDispenser({dispatchedDispenser.DispenserId});
                                        validReadyType = dispenserReady.Type == '{typeof(DispenserReady).Name}';
                                        Check(validReadyType) Error 'The request dispenser id:{dispatchedDispenser.DispenserId} is not a ready dispenser.' + dispenserReady.Type;
                                    }}
                                }}
                            }}
                            ", @$"
                            {{
                                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{dispatchedDispenser.Kind}');
                                dispenserReady = liquid.Outlet.FindDispenser({dispatchedDispenser.DispenserId});
                                dispenserReady.ClaimedWithdrawals(itIsThePresent, now, {{{dispatchedWithdrawlsComas}}});
                            }}
                            ");
                            if (!(resultWithdraws is OkObjectResult)) throw new Exception($"Failed to claim withdrawals for dispenser with ID {dispatchedDispenser.DispenserId}. Result: {resultWithdraws}");


                            resultWithdraws = LiquidityAPI.Liquidity.PerformQry(@$"
                            {{
                                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{dispatchedDispenser.Kind}');
                                dispenserReady = liquid.Outlet.FindDispenser({dispatchedDispenser.DispenserId});                                
                                
                                for (withdrawals : dispenserReady.ExplandedEnclosureWithdrawals)
                                {{
                                    withdrawal = withdrawals;
                                    print withdrawal.Withdrawal.Id id;
                                    print withdrawal.Withdrawal.Amount amount;
                                    print withdrawal.Withdrawal.Destination destination;
                                    print withdrawal.Withdrawal.AtAddress atAddress;
                                    print withdrawal.Withdrawal.Authorization authorization;
                                    print withdrawal.Withdrawal.StoreId storeId;
                                    print withdrawal.Withdrawal.Domain.Id domainId;
                                    print withdrawal.Withdrawal.Domain.Url domainUrl;
                                    print withdrawal.Withdrawal.Domain.AgentId agentId;
                                    print withdrawal.PullPaymentId pullPaymentId;

                                    print withdrawal.IsClaimed isClaimed;
                                    print withdrawal.IsCanceled isCanceled;
                                }}
                            }}
                            ");

                            jsonResult = (resultWithdraws as ObjectResult).Value.ToString();
                            var optionsWithdraws = new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                            var dispenserWithdrawals = System.Text.Json.JsonSerializer.Deserialize<DispenserTaskWithdrawals>(jsonResult, optionsWithdraws);
                            if (dispenserWithdrawals == null || dispenserWithdrawals.Withdrawals == null || !dispenserWithdrawals.Withdrawals.Any())
                            {
                                throw new Exception($"No withdrawals found for dispenser with ID {dispatchedDispenser.DispenserId}.");
                            }

                            foreach (var withdrawal in dispenserWithdrawals.Withdrawals)
                            {
                                if (withdrawal.IsClaimed && withdrawal.IsCanceled) throw new GameEngineException($"The withdrawal for dispenser with ID {dispatchedDispenser.DispenserId} cannot be both claimed and canceled. Withdrawal: {withdrawal.Amount} at {withdrawal.AtAddress}.");
                                if (!withdrawal.IsClaimed && !withdrawal.IsCanceled) throw new GameEngineException($"The withdrawal for dispenser with ID {dispatchedDispenser.DispenserId} is neither claimed nor canceled. Withdrawal: {withdrawal.Amount} at {withdrawal.AtAddress}.");
                            }

                            foreach (var withdrawal in dispenserWithdrawals.Withdrawals)
                            {
                                if (withdrawal.IsCanceled) PaymentManager.ArchivePullPayment(withdrawal.PullPaymentId);
                            }

                            foreach (var withdrawal in dispenserWithdrawals.Withdrawals)
                            {
                                MultipleProcessors multipleDrivers = WholePaymentProcessor.Instance().PaymentProcessorsAndActionsByDomains.SearchForX(withdrawal.AgentId, withdrawal.DomainUrl);
                                var entityId = 4; // fiero
                                if (withdrawal.IsClaimed)
                                {
                                    //driver de UNLOCKDEBIT
                                    PaymentProcessor unlockAndDebitProcessor = multipleDrivers.SearchByX(TransactionType.UnlockDebit, dispatchedDispenser.Kind, PaymentMethod.ThirdParty, entityId);
                                    if (unlockAndDebitProcessor == null) throw new Exception($"No unlock and withdrawal processor found for kind: {dispatchedDispenser.Kind} and entityId: {entityId}.");

                                    using (RecordSet recordSet = unlockAndDebitProcessor.GetRecordSet())
                                    {
                                        recordSet.SetParameter("atAddress", withdrawal.AtAddress);
                                        recordSet.SetParameter("amount", withdrawal.Amount);
                                        recordSet.SetParameter("accountNumber", dispatchedDispenser.Kind);
                                        recordSet.SetParameter("who", $"{this.GetType().Name}");
                                        recordSet.SetParameter("documentNumber", withdrawal.Authorization);
                                        recordSet.SetParameter("storeId", withdrawal.StoreId);
                                        recordSet.SetParameter("concept", $"Withdrawal for dispenser {dispatchedDispenser.DispenserId}");
                                        recordSet.SetParameter("reference", dispatchedDispenser.DispenserId);
                                        recordSet.SetParameter("processorId", unlockAndDebitProcessor.Id);
                                        recordSet.SetParameter("sourceNumber", 17);//Rubicon: DEFAULT SOURCENUMBER
                                        recordSet.SetParameter("sourceName", GamesEngine.Finance.TransactionMessage.NO_SOURCE_NAME);
                                        var driverResult = unlockAndDebitProcessor.Execute<DoneResponse>(DateTime.Now, recordSet);
                                        if (!driverResult.Done)
                                        {
                                            throw new Exception($"Failed to unlock and debit for dispenser with ID {dispatchedDispenser.DispenserId}. Withdrawal: {withdrawal.Amount} at {withdrawal.AtAddress}.");
                                        }
                                    }
                                }
                                else if (withdrawal.IsCanceled)
                                {
                                    //driver de UNLOCK
                                    PaymentProcessor unlockProcessor = multipleDrivers.SearchByX(TransactionType.Unlock, dispatchedDispenser.Kind, PaymentMethod.ThirdParty, entityId);
                                    if (unlockProcessor == null) throw new Exception($"No unlock processor found for kind: {dispatchedDispenser.Kind} and entityId: {entityId}.");

                                    using (RecordSet recordSet = unlockProcessor.GetRecordSet())
                                    {
                                        recordSet.SetParameter("atAddress", withdrawal.AtAddress);
                                        recordSet.SetParameter("amount", withdrawal.Amount);
                                        recordSet.SetParameter("accountNumber", dispatchedDispenser.Kind);
                                        recordSet.SetParameter("who", $"{this.GetType().Name}");
                                        recordSet.SetParameter("documentNumber", withdrawal.Authorization);
                                        recordSet.SetParameter("storeId", withdrawal.StoreId);
                                        recordSet.SetParameter("concept", $"Unlocked withdrawal {withdrawal.Id} from {withdrawal.DomainUrl}");
                                        recordSet.SetParameter("reference", withdrawal.Id);
                                        recordSet.SetParameter("processorId", unlockProcessor.Id);
                                        var driverResult = unlockProcessor.Execute<DoneResponse>(DateTime.Now, recordSet);
                                        if (driverResult == null) throw new Exception("Failed to create the refund transaction. The driver result is null.");
                                        if (!driverResult.Done) throw new Exception("Failed to unlock the deposit. The refund was not successful.");
                                    }
                                }
                                else
                                {
                                    throw new GameEngineException($"The withdrawal for dispenser with ID {dispatchedDispenser.DispenserId} cannot be both claimed and canceled. Withdrawal: {withdrawal.Amount} at {withdrawal.AtAddress}.");
                                }
                            }

                            var resultDispatchedDispenser = LiquidityAPI.Liquidity.PerformCmd(@$"
                            {{
                                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{dispatchedDispenser.Kind}');
                                dispenserReady = liquid.Outlet.FindDispenser({dispatchedDispenser.DispenserId});
                                dispenserReady.Discard(now);
                            }}
                            ");
                            if (!(resultDispatchedDispenser is OkObjectResult)) throw new Exception($"Failed to process dispenser with ID {dispatchedDispenser.DispenserId}. Result: {resultDispatchedDispenser}");
                            break;
                        case LiquidityMessageType.ExchangeRateChange:
                            var exchangeRateChangeMessage = new ExchangeRateChangeMessage(msg);
                            if (string.IsNullOrWhiteSpace(exchangeRateChangeMessage.Kind)) throw new ArgumentNullException(nameof(exchangeRateChangeMessage.Kind));
                            if (exchangeRateChangeMessage.Rate <= 0) throw new ArgumentNullException(nameof(exchangeRateChangeMessage.Rate));
                            ExchangeRate.LastKnown.AddOrUpdateRate(exchangeRateChangeMessage.Kind, exchangeRateChangeMessage.Rate);
                            // CMD LOCK: Send the exchange rate change event to the platform monitor
                            var resultSendEvent = LiquidityAPI.Liquidity.PerformCmd($"print company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{exchangeRateChangeMessage.Kind}').NotifyExchangeRateChange(now, {exchangeRateChangeMessage.Rate}) none;");                            
                            if (!(resultSendEvent is OkObjectResult)) throw new Exception($"Failed to send exchange rate change event for kind {exchangeRateChangeMessage.Kind}. Result: {resultSendEvent}");
                            break;
                        case LiquidityMessageType.DispenserWithdrawalsCommitted:
                            var dispenserWithdrawalsCommitted = new DispenserCommitWithdrawalsMessage(msg);

                            var withdrawalsResult = LiquidityAPI.Liquidity.PerformChkThenQry($@"
                            {{
                                existKind = company.System.LiquidFlow.ExistInstance('{dispenserWithdrawalsCommitted.Kind}');
                                Check(existKind) Error 'The request kind:{dispenserWithdrawalsCommitted.Kind} does not exist.';
                                if (existKind)
                                {{
                                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{dispenserWithdrawalsCommitted.Kind}');
                                    existDispenser = liquid.Outlet.ExistDispenser({dispenserWithdrawalsCommitted.Id});
                                    Check(existDispenser) Error 'The request dispenser id:{dispenserWithdrawalsCommitted.Id} does not exist.';
                                    if (existDispenser)
                                    {{
                                        dispenser = liquid.Outlet.FindDispenser({dispenserWithdrawalsCommitted.Id});
                                        isDispenserReady = dispenser.Type == '{typeof(DispenserReady).Name}';
                                        Check(isDispenserReady) Error 'The request dispenser id:{dispenserWithdrawalsCommitted.Id} is not ready to commit withdrawals.' + dispenser.Type;
                                        if (isDispenserReady)
                                        {{
                                            hasBeenCommitted = dispenser.HasBeenCommitted;
                                            Check(!hasBeenCommitted) Error 'The request dispenser id:{dispenserWithdrawalsCommitted.Id} has already been committed.';
                                        }}
                                    }}
                                }}
                            }}
                            ", $@"
                            {{
                                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{dispenserWithdrawalsCommitted.Kind}');
                                dispenser = liquid.Outlet.FindDispenser({dispenserWithdrawalsCommitted.Id});
                                for (withdrawals : dispenser.ExplandedEnclosureWithdrawals)
                                {{
                                    withdrawal = withdrawals;
                                    print withdrawal.Withdrawal.Id id;
                                    print withdrawal.Withdrawal.Amount amount;
                                    print withdrawal.Withdrawal.Destination destination;
                                    print withdrawal.Withdrawal.AtAddress atAddress;
                                    print withdrawal.Withdrawal.Authorization authorization;
                                    print withdrawal.Withdrawal.StoreId storeId;
                                    print withdrawal.Withdrawal.Domain.Id domainId;
                                    print withdrawal.Withdrawal.Domain.Url domainUrl;
                                    print withdrawal.Withdrawal.Domain.AgentId agentId;
                                    print withdrawal.PullPaymentId pullPaymentId;

                                    print withdrawal.IsClaimed isClaimed;
                                    print withdrawal.IsCanceled isCanceled;
                                }}
                            }}");
                            if (!(withdrawalsResult is OkObjectResult okResult)) throw new Exception($"Failed to retrieve withdrawals for dispenser with ID {dispenserWithdrawalsCommitted.Id}. Result: {withdrawalsResult}");

                            jsonResult = (withdrawalsResult as ObjectResult).Value.ToString();
                            optionsWithdraws = new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                            dispenserWithdrawals = System.Text.Json.JsonSerializer.Deserialize<DispenserTaskWithdrawals>(jsonResult, optionsWithdraws);
                            if (dispenserWithdrawals == null || dispenserWithdrawals.Withdrawals == null || !dispenserWithdrawals.Withdrawals.Any()) throw new Exception($"No withdrawals found for dispenser with ID {dispenserWithdrawalsCommitted.Id}.");

                            foreach (var withdrawal in dispenserWithdrawals.Withdrawals)
                            {
                                if (withdrawal.IsClaimed && withdrawal.IsCanceled) throw new GameEngineException($"The withdrawal for dispenser with ID {dispenserWithdrawalsCommitted.Id} cannot be both claimed and canceled. Withdrawal: {withdrawal.Amount} at {withdrawal.AtAddress}.");
                            }

                            List<(DispenserTaskWithdrawals.WithdrawalDTO Withdrawal, string PullPaymentId)> createdPullPayments = new();
                            foreach (var withdrawal in dispenserWithdrawals.Withdrawals)
                            {
                                var pullPayment = PaymentManager.CreatePullPayment($"Withdrawal {withdrawal.Id}", withdrawal.Amount, dispenserWithdrawalsCommitted.Kind, dispenserWithdrawalsCommitted.PaymentDockId);
                                if (pullPayment == null) throw new Exception($"Failed to create pull payment for withdrawal {withdrawal.Amount} at {withdrawal.AtAddress} for dispenser with ID {dispenserWithdrawalsCommitted.Id}.");
                                createdPullPayments.Add((withdrawal, pullPayment.Id));
                            }

                            StringBuilder cmdWithdrawalUpdatePullPayments = new();
                            foreach (var withdrawal in createdPullPayments)
                            {
                                cmdWithdrawalUpdatePullPayments.AppendLine($"dispenser.SetWithdrawalPullPaymentId({withdrawal.Withdrawal.Id}, '{withdrawal.PullPaymentId}');");
                            }

                            var dispenserPullPaymentUpdateResult = LiquidityAPI.Liquidity.PerformChkThenCmd($@"
                            {{
                                existKind = company.System.LiquidFlow.ExistInstance('{dispenserWithdrawalsCommitted.Kind}');
                                Check(existKind) Error 'The request kind:{dispenserWithdrawalsCommitted.Kind} does not exist.';
                                if (existKind)
                                {{
                                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{dispenserWithdrawalsCommitted.Kind}');
                                    existDispenser = liquid.Outlet.ExistDispenser({dispenserWithdrawalsCommitted.Id});
                                    Check(existDispenser) Error 'The request dispenser id:{dispenserWithdrawalsCommitted.Id} does not exist.';
                                    if (existDispenser)
                                    {{
                                        dispenser = liquid.Outlet.FindDispenser({dispenserWithdrawalsCommitted.Id});
                                        isDispenserReady = dispenser.Type == '{typeof(DispenserReady).Name}';
                                        Check(isDispenserReady) Error 'The request dispenser id:{dispenserWithdrawalsCommitted.Id} is not a ready dispenser.' + dispenser.Type;
                                        if (isDispenserReady)
                                        {{
                                            hasBeenCommitted = dispenser.HasBeenCommitted;
                                            Check(!hasBeenCommitted) Error 'The request dispenser id:{dispenserWithdrawalsCommitted.Id} has already been committed.';
                                        }}
                                    }}
                                }}
                            }}
                            ", $@"
                            {{
                                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{dispenserWithdrawalsCommitted.Kind}');
                                dispenser = liquid.Outlet.FindDispenser({dispenserWithdrawalsCommitted.Id});
                                {cmdWithdrawalUpdatePullPayments}
                                dispenser.CommitWithdrawals(itIsThePresent, now);
                            }}");
                            if (!(dispenserPullPaymentUpdateResult is OkObjectResult)) throw new Exception($"Failed to update pull payment ids for dispenser with ID {dispenserWithdrawalsCommitted.Id}. Result: {dispenserPullPaymentUpdateResult}");

                            foreach (var withdrawalPullPayment in createdPullPayments)
                            {
                                PaymentManager.ClaimPullPayment(withdrawalPullPayment.PullPaymentId, withdrawalPullPayment.Withdrawal.Destination, withdrawalPullPayment.Withdrawal.Amount, dispenserWithdrawalsCommitted.Kind);
                            }
                            break;
                        case LiquidityMessageType.AddedWithdrawalsCommitDispenser:
                            var addedWithdrawalsCommitDispenser = new AddedWithdrawalsCommitDispenserMessage(msg);

                            var queryForWithdrawal = LiquidityAPI.Liquidity.PerformChkThenQry($@"
                            {{
                                existKind = company.System.LiquidFlow.ExistInstance('{addedWithdrawalsCommitDispenser.Kind}');
                                Check(existKind) Error 'The request kind:{addedWithdrawalsCommitDispenser.Kind} does not exist.';
                                if (existKind)
                                {{
                                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{addedWithdrawalsCommitDispenser.Kind}');
                                    existDispenser = liquid.Outlet.ExistDispenser({addedWithdrawalsCommitDispenser.Id});
                                    Check(existDispenser) Error 'The request dispenser id:{addedWithdrawalsCommitDispenser.Id} does not exist.';
                                    if (existDispenser)
                                    {{
                                        dispenser = liquid.Outlet.FindDispenser({addedWithdrawalsCommitDispenser.Id});
                                        isDispenserReady = dispenser.Type == '{typeof(DispenserReady).Name}';
                                        Check(isDispenserReady) Error 'The request dispenser id:{addedWithdrawalsCommitDispenser.Id} is not a ready dispenser.' + dispenser.Type;
                                        if (isDispenserReady)
                                        {{
                                            hasBeenCommitted = dispenser.HasBeenCommitted;
                                            Check(hasBeenCommitted) Error 'The request dispenser id:{addedWithdrawalsCommitDispenser.Id} must be committed before adding withdrawals.';
                                            existWithdrawal = dispenser.ContainsWithdrawal({addedWithdrawalsCommitDispenser.WithdrawalId});
                                            Check(existWithdrawal) Error 'The request withdrawal id:{addedWithdrawalsCommitDispenser.WithdrawalId} does not exist in the dispenser.';
                                        }}
                                    }}
                                }}
                            }}
                            ",$@"
                            {{
                                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{addedWithdrawalsCommitDispenser.Kind}');
                                dispenser = liquid.Outlet.FindDispenser({addedWithdrawalsCommitDispenser.Id});
                                withdrawal = dispenser.FindEnclosureWithdrawal({addedWithdrawalsCommitDispenser.WithdrawalId});
                                print withdrawal.Withdrawal.Id id;
                                print withdrawal.Withdrawal.Amount amount;
                                print withdrawal.Withdrawal.AtAddress atAddress;
                                print withdrawal.Withdrawal.Authorization authorization;
                                print withdrawal.Withdrawal.StoreId storeId;
                                print withdrawal.Withdrawal.Domain.Id domainId;
                                print withdrawal.Withdrawal.Domain.Url domainUrl;
                                print withdrawal.Withdrawal.Domain.AgentId agentId;
                                print withdrawal.PullPaymentId pullPaymentId;
                                print withdrawal.IsClaimed isClaimed;
                                print withdrawal.IsCanceled isCanceled;
                            }}");

                            if (!(queryForWithdrawal is OkObjectResult okResultForWithdrawal)) throw new Exception($"Failed to retrieve withdrawal {addedWithdrawalsCommitDispenser.WithdrawalId} for dispenser with ID {addedWithdrawalsCommitDispenser.Id}. Result: {queryForWithdrawal}");

                            string jsonResultForWithdrawal = (queryForWithdrawal as ObjectResult).Value.ToString();
                            var optionsForWithdrawal = new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                            var dispenserWithdrawalDTO = System.Text.Json.JsonSerializer.Deserialize<DispenserTaskWithdrawals.WithdrawalDTO>(jsonResultForWithdrawal, optionsForWithdrawal);
                            if (dispenserWithdrawalDTO == null) throw new Exception($"No withdrawal found with ID {addedWithdrawalsCommitDispenser.WithdrawalId} for dispenser with ID {addedWithdrawalsCommitDispenser.Id}.");

                            var pullPaymentForWithdrawal = PaymentManager.CreatePullPayment($"Withdrawal {dispenserWithdrawalDTO.Id}", dispenserWithdrawalDTO.Amount, addedWithdrawalsCommitDispenser.Kind);
                            var updateWithdrawalsResult = LiquidityAPI.Liquidity.PerformCmd($@"
                            {{
                                liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{addedWithdrawalsCommitDispenser.Kind}');
                                dispenser = liquid.Outlet.FindDispenser({addedWithdrawalsCommitDispenser.Id});
                                dispenser.AddWithdrawalToTask(itIsThePresent, now, {dispenserWithdrawalDTO.Id}, '{pullPaymentForWithdrawal.Id}');
                            }}");
                            if (!(updateWithdrawalsResult is OkObjectResult)) throw new Exception($"Failed to update withdrawal {addedWithdrawalsCommitDispenser.WithdrawalId} with pull payment id for dispenser with ID {addedWithdrawalsCommitDispenser.Id}. Result: {updateWithdrawalsResult}");

                            break;
                        default:
                            throw new NotSupportedException($"The message type {messageType} is not supported in {this.GetType().Name}.");
                    }
                }
                catch (Exception e)
                {
                    ErrorsSender.Send(e, $"message {msg}", $"Consumer:{this.GetType().Name}", $"topic:{Topic}");
                }
            }

            private IActionResult ConfirmDeposit(ConfirmedDepositDueBody body)
            {
                var kind = body.Kind;
                var depositId = body.DepositId;

                var resultRate = ExchangeRate.LastKnown[kind];
                if (!resultRate.IsSuccess) throw new GameEngineException($"Exchange rate from {kind} not found.");

                var depositDataQuery = RetriveDepositData(kind, depositId, resultRate.Value);
                if (!(depositDataQuery is OkObjectResult)) return depositDataQuery;
                OkObjectResult o = (OkObjectResult)depositDataQuery;
                string json = o.Value.ToString();
                var deposit = JsonConvert.DeserializeObject<RetrieveDepositData>(json);
                if (deposit == null) throw new GameEngineException("Failed to retrieve deposit data.");
                if (!deposit.IsPending) throw new GameEngineException($"The deposit {depositId} is not in a pending state. Current state: {(deposit.IsConfirmed ? "Confirmed" : "Canceled")}");

                //string who = Security.UserName(HttpContext);
                string who = "Liquidity";

                DueStatus status = DueStatus.Paid;
                //SI EL PENDIENTE NO ES 0, SE DEBE HACER UN REEMBOLSO
                if (body.Due != 0)
                {
                    var resetDepositResult = UnlockDebitMovement(kind, deposit, who);
                    if (!resetDepositResult) throw new GameEngineException("Failed to unlock and debit the deposit before remaking it.");

                    if (body.Due < 0)//Overpaid
                    {
                        status = DueStatus.Overpaid;
                    }
                    else if (body.Due > 0)//Underpaid
                    {
                        status = DueStatus.Underpaid;
                    }
                    int newOverpaidDepositId = RemakeDeposit(
                        depositId,
                        kind,
                        deposit.DomainUrl,
                        deposit.InvoiceId,
                        deposit.Destination,
                        deposit.PaymentLink,
                        deposit.ExternalAtAddress,
                        deposit.ExternalReference,
                        who,
                        body.TotalPaid,
                        body.Rate,
                        totalConfirmations: 0,
                        deposit.ConfirmedCurrency,
                        deposit.StoreId
                    );

                    depositDataQuery = RetriveDepositData(kind, newOverpaidDepositId, resultRate.Value);
                    if (!(depositDataQuery is OkObjectResult)) return depositDataQuery;
                    o = (OkObjectResult)depositDataQuery;
                    json = o.Value.ToString();
                    deposit = JsonConvert.DeserializeObject<RetrieveDepositData>(json);
                    if (deposit == null) throw new GameEngineException("Failed to retrieve deposit data.");
                    if (!deposit.IsPending) throw new GameEngineException($"The deposit {newOverpaidDepositId} is still in a pending state after remaking the deposit movements.");
                }

                MultipleProcessors multipleDrivers = WholePaymentProcessor.Instance().PaymentProcessorsAndActionsByDomains.SearchForX(deposit.AgentId, deposit.DomainUrl);
                var entityId = 4; // fiero
                PaymentProcessor refundProcessor = multipleDrivers.SearchByX(TransactionType.Unlock, kind, PaymentMethod.ThirdParty, entityId);
                if (refundProcessor == null) throw new GameEngineException($"No unlock processor found for kind: {kind} and entityId: {entityId}.");

                bool unlockDepositResult = false;
                using (RecordSet recordSet = refundProcessor.GetRecordSet())
                {
                    recordSet.SetParameter("atAddress", deposit.ExternalAtAddress);
                    recordSet.SetParameter("amount", deposit.Amount);
                    recordSet.SetParameter("accountNumber", kind);
                    recordSet.SetParameter("who", who);
                    recordSet.SetParameter("documentNumber", deposit.AuthorizationId);
                    recordSet.SetParameter("storeId", deposit.StoreId);
                    recordSet.SetParameter("concept", $"Unlocked deposit {deposit.Id} from {deposit.DomainUrl}");
                    recordSet.SetParameter("reference", deposit.Id);
                    recordSet.SetParameter("processorId", refundProcessor.Id);
                    var driverResult = refundProcessor.Execute<DoneResponse>(DateTime.Now, recordSet);
                    if (driverResult == null) throw new GameEngineException("Failed to create the refund transaction. The driver result is null.");
                    unlockDepositResult = driverResult.Done;
                }
                if (!unlockDepositResult) throw new GameEngineException("Failed to unlock the deposit. The refund was not successful.");

                string channel = "Deposit";
                string eventType = $"Deposit.{kind}.Approve";
                var externalHookProperties = new Dictionary<string, object>
                {
                    { "reference", deposit.ExternalReference },
                    { "atAddress", deposit.ExternalAtAddress },
                    { "status", $"{status}" },
                    { "amount", body.TotalPaid * body.Rate },
                    { "storeId", deposit.StoreId },
                    { "depositId", deposit.Id}
                };
                if (WebHookClient.IsConfigured)
                {
                    _ = WebHookClient.Instance.SendWebHookAsync(DateTime.Now, externalHookProperties, eventType, channel);
                }

                var result = LiquidityAPI.Liquidity.PerformChkThenCmd($@"
                {{
                    existKind = company.System.LiquidFlow.ExistInstance('{kind}');
				    Check(existKind) Error 'The request kind:{kind} does not exist.';
                    if (existKind)
                    {{
                        liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');

                        existDeposit = liquid.Source.Jar.ExistDeposit({deposit.Id});
                        Check(existDeposit) Error 'The deposit:{deposit.Id} does not exist.';
                        if (existDeposit)
                        {{
                            enclosureDeposit = liquid.Source.Jar.FindEnclosureDeposit({deposit.Id});  

                            validDepositType = enclosureDeposit.IsPending;
                            Check(validDepositType) Error 'The deposit:{deposit.Id} is not a pending type.';

                            domain = company.Sales.DomainFrom('{deposit.DomainUrl}');
                            existPaymentDock = liquid.ParentFlow.ExistPaymentEngineDock(domain);
                            Check(existPaymentDock) Error 'The payment engine dock for the domain:{deposit.DomainUrl} does not exist.';
                        }}
                    }}
                }}
                ", $@"
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    deposit = liquid.Source.Jar.FindDepositById({deposit.Id});
                    confirmedDeposit = liquid.Source.ConfirmDeposit(itIsThePresent, Now, deposit);
                    print confirmedDeposit.Id id;
                }}");

                if (!(result is OkObjectResult)) throw new GameEngineException("Failed to confirm the deposit.");
                return result;
            }

            private IActionResult RetriveDepositData(string kind, int depositId, decimal rate)
            {
                if (string.IsNullOrWhiteSpace(kind)) throw new ArgumentException("kind is required", nameof(kind));
                if (depositId <= 0) throw new ArgumentException("depositId must be greater than zero", nameof(depositId));
                var depositQuery = LiquidityAPI.Liquidity.PerformChkThenQry($@"
                {{
                    existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                    Check(existKind) Error 'The request kind:{kind} does not exist.';
                    if (existKind)
                    {{
                        liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');

                        existDeposit = liquid.Source.Jar.ExistDeposit({depositId});
                        Check(existDeposit) Error 'The deposit:{depositId} does not exist.';
                    }}
                }}
                ", $@"
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    deposit = liquid.Source.Jar.FindDepositById({depositId});

                    print liquid.Source.Jar.Address address;

                    print deposit.Id id;
                    print deposit.InvoiceId invoiceId;
                    print deposit.AuthorizationId authorizationId;
                    print deposit.Amount amount;
                    print deposit.Rate rate;
                    print deposit.ConfirmedAmount confirmedAmount;
                    print deposit.ConfirmedCurrency confirmedCurrency;
                    print deposit.Address depositAddress;
                    print deposit.StoreId storeId;
                    print deposit.Domain.Id domainId;
                    print deposit.Domain.Url domainUrl;
                    print deposit.Domain.AgentId agentId;
                    print deposit.ExternalReference externalReference;
                    print deposit.ExternalAtAddress externalAtAddress;
                    print deposit.Destination destination;
                    print deposit.PaymentLink paymentLink;
                    print deposit.ExchangeVariation({rate}) exchangeRateVariation;
                    print deposit.ValueDirectionAsString({rate}) valueDirection;
                
                    enclosureDeposit = liquid.Source.Jar.FindEnclosureDeposit(deposit.Id);  
                    print enclosureDeposit.IsPending isPending;
                    print enclosureDeposit.IsConfirmed isConfirmed;
                    print enclosureDeposit.IsCanceled isCanceled;
                }}
                ");
                return depositQuery;
            }

            private int RemakeDeposit(int depositId, string kind, string domainUrl, string invoiceId, string destination, string paymentLink, string externalAtAddress, int externalReference, string who, decimal totalPayedAmount, decimal rate, int totalConfirmations, string confirmedCurrency, int storeId)
            {
                var cancelOldDepositCmd = LiquidityAPI.Liquidity.PerformChkThenCmd($@"
                {{
                    existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                    Check(existKind) Error 'The request kind:{kind} does not exist.';
                    if (existKind)
                    {{
                        liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');

                        existDeposit = liquid.Source.Jar.ExistDeposit({depositId});
                        if (existDeposit)
                        {{
                            enclosureDeposit = liquid.Source.Jar.FindEnclosureDeposit({depositId});
                            validDepositType = enclosureDeposit.IsPending;
                            Check(validDepositType) Error 'The deposit:{depositId} is not a pending type.';
                        }}
                    }}
                }}
                ", $@"
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    deposit = liquid.Source.Jar.FindDepositById({depositId});
                    canceledDeposit = liquid.Source.Jar.CancelDeposit(itIsThePresent, now, deposit);
                    print canceledDeposit.Id id;
                }}");
                if (!(cancelOldDepositCmd is OkObjectResult)) throw new GameEngineException($"Failed to cancel the old deposit id {depositId} command.");

                var newOverpaidDeposit = RetrieveNextDeposit(kind, domainUrl);
                if (!(newOverpaidDeposit is OkObjectResult)) throw new GameEngineException($"Failed to retrieve next deposit for kind: {kind} and domain: {domainUrl}.");
                OkObjectResult o = (OkObjectResult)newOverpaidDeposit;
                string json = o.Value.ToString();
                var newOverpaidDepositData = JsonConvert.DeserializeObject<NextDepositDTO>(json);
                if (newOverpaidDepositData == null) throw new GameEngineException("Failed to retrieve next deposit data for Overpaid deposit.");

                int newDepositAuthorization = CreditDepositAndLock(kind, newOverpaidDepositData, externalAtAddress, who, totalPayedAmount, rate);
                if (newDepositAuthorization == ASITenantDriver.FAKE_TICKET_NUMBER) throw new GameEngineException("Could not get a valid authorization ID for the new Overpaid deposit.");

                decimal newConfirmedAmount = totalPayedAmount * rate; // Rubicon: El monto confirmado es el monto pagado por la tasa de cambio actual.

                var depositCommand = CreateDraftDeposit(
                    kind,
                    domainUrl,
                    externalAtAddress,
                    newOverpaidDepositData.DepositId,
                    invoiceId,
                    newDepositAuthorization,
                    externalReference,
                    destination,
                    paymentLink,
                    totalPayedAmount,
                    rate,
                    confirmedCurrency,
                    newConfirmedAmount,
                    totalConfirmations,
                    storeId
                );
                if (!(depositCommand is OkObjectResult)) throw new GameEngineException("Failed to create the new deposit command.");

                return newOverpaidDepositData.DepositId;
            }

            private IActionResult CreateDraftDeposit(string kind, string domain, string atAddress, int depositId, string invoiceId, int depositAuthorization, int externalReference, string invoiceDestination, string invoicePaymentLink, decimal exchangeAmount, decimal exchangeRate, string confirmedCurrency, decimal confirmedAmount, int totalConfirmations, int storeId)
            {
                var result = LiquidityAPI.Liquidity.PerformChkThenCmd($@"
                {{
                    existsDomain = company.Sales.ExistsDomain('{domain}');
                    Check(existsDomain) Error 'The domain:{domain} does not exist.';
                    if (existsDomain)
                    {{
                        existKind = company.System.LiquidFlow.ExistInstance('{kind}');
				        Check(existKind) Error 'The request kind:{kind} does not exist.';

                        if (existKind)
                        {{
                            domain = company.Sales.DomainFrom('{domain}');
                            liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                            existPaymentDock = liquid.ParentFlow.ExistPaymentEngineDock(domain);
                            Check(existPaymentDock) Error 'The payment engine dock for the domain:{domain} does not exist.';
                        }}
                    }}
                }}
                ", $@"
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    domain = company.Sales.DomainFrom('{domain}');
                    depositId = {depositId};
                    draftDepositConfirmation = liquid.Source.CreateDraftDeposit(itIsThePresent, Now, depositId, '{invoiceId}', {depositAuthorization}, '{atAddress}', {externalReference}, '{invoiceDestination}', {exchangeAmount}, {exchangeRate}, '{confirmedCurrency}', {confirmedAmount}, {totalConfirmations}, {storeId}, domain);
                    draftDepositConfirmation.PaymentLink = '{invoicePaymentLink}';
                    print draftDepositConfirmation.Id depositId;
                    print draftDepositConfirmation.AuthorizationId authorizationId;
                    print draftDepositConfirmation.InvoiceId invoiceId;
                }}");
                return result;
            }

            private bool UnlockDebitMovement(string kind, RetrieveDepositData depositData, string who)
            {
                MultipleProcessors multipleDrivers = WholePaymentProcessor.Instance().PaymentProcessorsAndActionsByDomains.SearchForX(depositData.AgentId, depositData.DomainUrl);
                var entityId = 4; // fiero
                PaymentProcessor unlockAndDebitProcessor = multipleDrivers.SearchByX(TransactionType.UnlockDebit, kind, PaymentMethod.ThirdParty, entityId);
                if (unlockAndDebitProcessor == null) throw new GameEngineException($"No unlock and withdrawal processor found for kind: {kind} and entityId: {entityId}.");

                using (RecordSet recordSet = unlockAndDebitProcessor.GetRecordSet())
                {
                    recordSet.SetParameter("atAddress", depositData.ExternalAtAddress);
                    recordSet.SetParameter("amount", depositData.Amount);
                    recordSet.SetParameter("accountNumber", kind);
                    recordSet.SetParameter("who", who);
                    recordSet.SetParameter("sourceNumber", 17);
                    recordSet.SetParameter("sourceName", GamesEngine.Finance.TransactionMessage.NO_SOURCE_NAME);
                    recordSet.SetParameter("documentNumber", depositData.AuthorizationId);
                    recordSet.SetParameter("storeId", depositData.StoreId);
                    recordSet.SetParameter("concept", $"Unlocked and debited deposit {depositData.Id} from {depositData.DomainUrl}");
                    recordSet.SetParameter("reference", depositData.Id);
                    recordSet.SetParameter("processorId", unlockAndDebitProcessor.Id);
                    var driverResult = unlockAndDebitProcessor.Execute<DoneResponse>(DateTime.Now, recordSet);
                    if (driverResult == null) throw new GameEngineException("Failed to create the refund transaction. The driver result is null.");

                    return driverResult.Done;
                }
            }

            private IActionResult RetrieveNextDeposit(string kind, string domain)
            {
                if (string.IsNullOrWhiteSpace(domain)) throw new GameEngineException("domain is required");
                if (string.IsNullOrWhiteSpace(kind)) throw new GameEngineException("kind is required");

                var depositIdQuery = LiquidityAPI.Liquidity.PerformChkThenQry($@"
                {{
                    existDomain = company.Sales.ExistsDomain('{domain}');
                    Check(existDomain) Error 'The domain:{domain} does not exist.';
                    if (existDomain)
                    {{
                        existKind = company.System.LiquidFlow.ExistInstance('{kind}');
                        Check(existKind) Error 'The request kind:{kind} does not exist.';

                        if (existKind)
                        {{
                            liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                            domain = company.Sales.DomainFrom('{domain}');
                            existPaymentDock = liquid.ParentFlow.ExistPaymentEngineDock(domain);
                            Check(existPaymentDock) Error 'The payment engine dock for the domain:{domain} does not exist.';
                        }}
                    }}
                }}
                ", $@"
                {{
                    liquid = company.System.LiquidFlow.GetOrCreateLiquid(itIsThePresent, now, '{kind}');
                    Eval('depositId = '+liquid.Source.NextDepositId()+';');
                    print depositId depositId;

                    domain = company.Sales.DomainFrom('{domain}');
                    print domain.Id domainId;
                    print domain.Url domainUrl;
                    print domain.AgentId agentId;
                
                    print company.Sales.CurrentStore.Id storeId;

                    paymentDock = liquid.ParentFlow.FindPaymentEngineDock(domain);
                    print paymentDock.Dock paymentDockId;
                }}");
                return depositIdQuery;
            }

            private int CreditDepositAndLock(string kind, NextDepositDTO nexDepositData, string atAddress, string who, decimal exchangeAmount, decimal exchangeRate)
            {
                int depositAuthorization = -1;
                MultipleProcessors drivers = WholePaymentProcessor.Instance().PaymentProcessorsAndActionsByDomains.SearchForX(nexDepositData.AgentId, nexDepositData.DomainUrl);
                PaymentProcessor paymentProcessor = drivers.SearchByX(TransactionType.Deposit, kind, PaymentMethod.ThirdParty, entityId: 4);//Drivers: entityId 4 means FIERO segun Driver de Deposit
                using (RecordSet recordSet = paymentProcessor.GetRecordSet())
                {
                    recordSet.SetParameter("amount", exchangeAmount);
                    recordSet.SetParameter("accountNumber", kind);
                    recordSet.SetParameter("currency", kind);
                    recordSet.SetParameter("atAddress", atAddress);
                    recordSet.SetParameter("domain", nexDepositData.DomainUrl);
                    recordSet.SetParameter("sourceNumber", 17);
                    recordSet.SetParameter("sourceName", GamesEngine.Finance.TransactionMessage.NO_SOURCE_NAME);
                    recordSet.SetParameter("who", who);
                    recordSet.SetParameter("agent", nexDepositData.AgentId);
                    recordSet.SetParameter("storeId", nexDepositData.StoreId);
                    recordSet.SetParameter("processorId", paymentProcessor.Id);
                    recordSet.SetParameter("description", $"Confirmed and Locked deposit {nexDepositData.DepositId} from {nexDepositData.DomainUrl}");
                    recordSet.SetParameter("reference", nexDepositData.DepositId);
                    recordSet.SetParameter("withLock", true);

                    var driverResult = paymentProcessor.Execute<DepositTransaction>(DateTime.Now, recordSet);
                    depositAuthorization = driverResult.AuthorizationId;
                }
                return depositAuthorization;
            }

            internal class DispenserTaskWithdrawals
            {
                public List<WithdrawalDTO> Withdrawals { get; set; }
                internal class WithdrawalDTO
                {
                    public int Id { get; set; }
                    public decimal Amount { get; set; }
                    public string Destination { get; set; }
                    public string AtAddress { get; set; }
                    public int Authorization { get; set; }
                    public int StoreId { get; set; }
                    public int DomainId { get; set; }
                    public string DomainUrl { get; set; }
                    public int AgentId { get; set; }
                    public bool IsClaimed { get; set; }
                    public bool IsCanceled { get; set; }
                    public string PullPaymentId { get; set; }
                }
            }

            internal class TankerDepositsDTO
            {
                public List<TankerDeposit> TankerTaskDeposits { get; set; }

                internal class TankerDeposit
                {
                    public int Id { get; set; }
                    public decimal Available { get; set; }
                    public TankerDeposit(int id, decimal available)
                    {
                        Id = id;
                        Available = available;
                    }
                }
            }
        }
    }
}
