﻿using GamesEngine.Business.Liquidity.Sentinels.Currency;
using GamesEngine.Business.Liquidity.Sentinels.Inbound;
using GamesEngine.Business.Liquidity.Transactions;
using GamesEngine.Finance;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using static GamesEngine.Business.Liquidity.Containers.Tanker;
using static GamesEngine.Business.Liquidity.PaymentEngineDocks;
using static GamesEngine.Settings.PaymentManager;

namespace GamesEngine.Business.Liquidity.Sentinels
{
    internal abstract class IngressSentinel : Sentinel
    {
        private readonly ConcurrentDictionary<int, EnclosureDeposit> enclosureDeposits = new ConcurrentDictionary<int, EnclosureDeposit>();

        internal IngressSentinel(): base() { }

        internal IEnumerable<SentinelTask> InboundTasks => sentinelTasks.Tasks;

        internal IEnumerable<Deposit> Deposits => enclosureDeposits.Values.Select(ed => ed.Deposit);

        internal void AddTask(SentinelTask task)
        {
            if (task == null) throw new ArgumentNullException(nameof(task));
            if (HasTask(task)) throw new GameEngineException("The task is already being watched by the sentinel.");

            sentinelTasks.AddTask(task, task);
        }

        internal void AwaitForTanker(bool itIsThePresent, TankerSealed tanker)
        {
            if (tanker == null) throw new ArgumentNullException(nameof(tanker));
            
            try
            {
                var currentDeposits = tanker.Deposits;
                if (currentDeposits == null || !currentDeposits.Any()) throw new GameEngineException("Tanker must have at least one deposit.");
                sentinelTasks.AwaitForTanker(tanker);
            }
            catch
            {
                Loggers.GetIntance().Sentinel.Error($"Error adding confirmation watchers for tanker {tanker.Id}. It may not be being watched by the sentinel.", null);
            }
        }

        internal void UndisburdenTanker(TankerSealed tankerSealed)
        {
            if (tankerSealed == null) throw new ArgumentNullException(nameof(tankerSealed), "Tanker cannot be null.");
            try
            {
                TankerInboundTask inboundTask = sentinelTasks.FindTask(tankerSealed) as TankerInboundTask;
                if (inboundTask == null) throw new GameEngineException($"No inbound task found for tanker {tankerSealed.Id}.");
                inboundTask.Undisburden();
            }
            catch (Exception e)
            {
                Loggers.GetIntance().Sentinel.Error($"Error undisburdening tanker {tankerSealed.Id}: {e.Message}", e);
            }
        }

        internal void AwaitForDeposit(EnclosureDeposit pendingDeposit, int totalConfirmations, PaymentEngineDock engineDock)
        {
            if (pendingDeposit == null) throw new ArgumentNullException(nameof(pendingDeposit), "Pending deposit cannot be null.");
            if (totalConfirmations < 0) throw new GameEngineException("Total confirmations must be greater than or equal to zero.");
            if (engineDock == null) throw new ArgumentNullException(nameof(engineDock), "Payment engine dock cannot be null.");

            try
            {
                enclosureDeposits.TryAdd(pendingDeposit.Id, pendingDeposit);
                sentinelTasks.AddConfirmationsWatcher(pendingDeposit.Deposit, totalConfirmations, engineDock);
            }
            catch
            {
                Loggers.GetIntance().Sentinel.Error($"Error adding confirmation watcher for deposit {pendingDeposit.Id}. It may not be being watched by the sentinel.", null);
            }
        }

        internal bool ExistInvoiceInDeposits(string invoiceId)
        {
            if (string.IsNullOrWhiteSpace(invoiceId)) throw new ArgumentNullException(nameof(invoiceId));

            return enclosureDeposits.Values.Any(ed => ed.Deposit.InvoiceId == invoiceId);
        }

        internal Deposit FindDepositByInvoice(string invoiceId)
        {
            if (string.IsNullOrWhiteSpace(invoiceId)) throw new ArgumentNullException(nameof(invoiceId));
            foreach (var ed in enclosureDeposits.Values)
            {
                if (ed.Deposit.InvoiceId == invoiceId) return ed.Deposit;
            }
            throw new GameEngineException($"Deposit with invoice ID {invoiceId} not found in the whole deposits.");
        }

        internal void InvoiceReceivedPayment(bool itIsThePresent, DateTime now, string invoiceId, decimal invoiceDue, decimal totalPaid, decimal rate, string transactionId, decimal transactionFee, string destination, string currency, string paymentDockId)
        {
            if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now));
            if (string.IsNullOrWhiteSpace(invoiceId)) throw new ArgumentNullException(nameof(invoiceId));
            if (string.IsNullOrWhiteSpace(transactionId)) throw new ArgumentNullException(nameof(transactionId));
            if (string.IsNullOrWhiteSpace(paymentDockId)) throw new ArgumentNullException(nameof(paymentDockId));
            if (string.IsNullOrWhiteSpace(destination)) throw new ArgumentNullException(nameof(destination));
            if (string.IsNullOrWhiteSpace(currency)) throw new ArgumentNullException(nameof(currency));
            if (totalPaid <= 0) throw new GameEngineException("Total paid must be greater than zero.");
            if (rate <= 0) throw new GameEngineException("Rate must be greater than zero.");
            if (transactionFee < 0) throw new GameEngineException("Transaction fee cannot be negative.");

            if (Integration.UseKafka && itIsThePresent)
            {
                using var buffer = new KafkaMessagesBuffer(itIsThePresent, $"{Integration.Kafka.TopicForContainerEvents}");
                var invoicePaidMessage = new InvoicePaidMessage(
                    invoiceId,
                    currency,
                    destination,
                    totalPaid,
                    transactionId,
                    transactionFee,
                    now
                );
                buffer.Send(invoicePaidMessage);
            }

            EnclosureDeposit matchingDeposit = null;
            foreach (var ed in enclosureDeposits.Values)
            {
                if (ed.Deposit.InvoiceId == invoiceId)
                {
                    matchingDeposit = ed;
                    break;
                }
            }
            if (matchingDeposit == null) throw new GameEngineException($"Deposit with invoice ID {invoiceId} not found in the whole deposits.");
            if (!matchingDeposit.IsPending) return;

            ConfirmationsInboundTask inboundTask = sentinelTasks.FindTask(matchingDeposit.Deposit) as ConfirmationsInboundTask;
            if (inboundTask == null) throw new GameEngineException($"No inbound task found for invoice ID: {invoiceId}.");

            if (inboundTask.PaymentEngineDock?.Dock != paymentDockId) throw new GameEngineException($"Payment dock ID {paymentDockId} does not match the expected dock ID {inboundTask.PaymentEngineDock.Dock} for invoice {invoiceId}.");
            inboundTask.InitializeInvoice(invoiceId, invoiceDue, totalPaid, rate, transactionId, paymentDockId);
            matchingDeposit.SetTransactionId(transactionId);

            if (itIsThePresent)
            {
                inboundTask.StartTask(now);                
                _ = DepositInprocessAsync(now, matchingDeposit.Deposit);
            }
        }

        private async Task DepositInprocessAsync(DateTime now, Deposit draftConfirmDeposit)//CAMBIAR POR COLA
        {
            if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now));
            if (draftConfirmDeposit == null) throw new ArgumentNullException(nameof(draftConfirmDeposit));

            try
            {
                if (!TownSettings.TryToGetActorUrl("LIQUIDITY", out string hostnameIp)) throw new GameEngineException($"Hostname or IP address is not configured for LIQUIDITY.");

                var client = new HttpClient();
                using var request = new HttpRequestMessage(HttpMethod.Post, $"{hostnameIp}/api/liquidity/{draftConfirmDeposit.Kind}/deposit/{draftConfirmDeposit.Id}/inprocess");

                HttpResponseMessage response = await client.SendAsync(request);
                if (response.IsSuccessStatusCode)
                {
                    Loggers.GetIntance().Sentinel.Debug($"Confirmed deposit {draftConfirmDeposit.Id} at {now} successfully notified.");
                }
                else
                {
                    Loggers.GetIntance().Sentinel.Debug($"Failed to notify confirmed deposit {draftConfirmDeposit.Id} at {now}. Status code: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Loggers.GetIntance().Sentinel.Error($"Error confirming deposit {draftConfirmDeposit.Id} at {now}: {ex.Message}", ex);
            }
        }

        internal static IngressSentinel IngressSentinelByKind(Liquid liquid, string kind)
        {
            if (liquid == null) throw new ArgumentNullException(nameof(liquid));
            if (string.IsNullOrWhiteSpace(kind)) throw new ArgumentNullException(nameof(kind));
            if (!Enum.TryParse(kind, out Currencies.CODES currencyCode)) throw new GameEngineException($"Invalid currency code: {kind}");

            IngressSentinel result = null;
            switch (currencyCode)
            {
                case Currencies.CODES.BTC:
                    result = new BTCIngressSentinel();
                    break;
                case Currencies.CODES.ETH:
                    result = new ETHIngressSentinel();
                    break;
                case Currencies.CODES.LTC:
                    result = new LTCIngressSentinel();
                    break;
                default:
                    throw new ArgumentException($"Unsupported currency code: {kind}", nameof(kind));
            }
            return result;
        }

        internal class AccountAmount
        {
            public string Account { get; private set; }
            public decimal Amount { get; private set; }
            public AccountAmount(string account, decimal amount)
            {
                if (string.IsNullOrWhiteSpace(account)) throw new ArgumentNullException(nameof(account));
                if (amount < 0) throw new ArgumentNullException(nameof(amount));
                Account = account;
                Amount = amount;
            }
            public void AppendAmount(decimal amount)
            {
                if (amount < 0) throw new ArgumentNullException(nameof(amount));
                Amount += amount;
            }
        }
    }
}
