﻿using GamesEngine.Business.Liquidity.Transactions;
using GamesEngine.Settings;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using static GamesEngine.Business.Liquidity.Containers.Dispenser;
using static GamesEngine.Business.Liquidity.Containers.Dispenser.DispenserReady;

namespace GamesEngine.Business.Liquidity.Sentinels.Outbound
{
    internal class ConfirmationsOutboundTask : OutboundTask
    {
        internal SentinelTasks SentinelOutboundTasks { get; private set; }
        internal CancellationTokenSource Cancellation { get; private set; } = new();

        private ConcurrentDictionary<Withdrawal, PayoutInfo> withdrawalPayoutConfirms = new();
        internal int ConfirmedWithdrawals => withdrawalPayoutConfirms.Count(w => w.Value != null);
        internal int TotalWithdrawals => withdrawalPayoutConfirms.Count;
        internal IEnumerable<Withdrawal> Withdrawals => withdrawalPayoutConfirms.Keys;
        

        internal DispenserReady Dispenser { get; private set; }

        internal int DispenserId => Dispenser.Id;

        internal DateTime Created { get; private set; }

        internal ConfirmationsOutboundTask(DateTime now , SentinelTasks outboundTasks, DispenserReady dispenserReady)
        {
            if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now));
            if (outboundTasks == null) throw new ArgumentNullException(nameof(outboundTasks));
            if (dispenserReady == null) throw new ArgumentNullException(nameof(dispenserReady));

            Created = now;
            SentinelOutboundTasks = outboundTasks;
            this.Dispenser = dispenserReady;
        }

        private DateTime runningTaskSince = DateTime.MinValue;
        internal override void StartTask(DateTime startedAt)
        {
            if (startedAt == DateTime.MinValue) throw new ArgumentNullException(nameof(startedAt));

            runningTaskSince = startedAt;
            if (task == null)
            {   
                task = Task.Run(async () =>
                {
                    if (!withdrawalPayoutConfirms.Any())
                    {
                        foreach (var enclosureWithdrawal in Dispenser.ExplandedEnclosureWithdrawals)
                        {
                            if (withdrawalPayoutConfirms.ContainsKey(enclosureWithdrawal.Withdrawal)) continue;
                            withdrawalPayoutConfirms.TryAdd(enclosureWithdrawal.Withdrawal, null);
                        }
                    }

                    while (withdrawalPayoutConfirms.Any(w => w.Value == null) && !Cancellation.IsCancellationRequested)
                    {
                        await Task.Delay(SentinelTasks.DELAY_SECONDS_PER_CONFIRMATION * 1000);
                        if (DateTime.Now - runningTaskSince >= TimeSpan.FromDays(30)) break;
                    }
                    if (Cancellation.IsCancellationRequested) return;

                    if (Integration.UseKafka)
                    {
                        using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(true, Integration.Kafka.TopicForLiquidityEvents))
                        {
                            List<int> withdrawalConfirms = withdrawalPayoutConfirms.Where(w => w.Value != null).Select(w => w.Key.Id).ToList();
                            var message = new DispatchedDispenserMessage(Dispenser.Kind, Dispenser.Id, DateTime.Now, withdrawalConfirms);
                            buffer.Send(message);
                        }
                    }
                });
            }
        }

        internal void ConfirmWithdrawal(Withdrawal withdrawal, string payoutId, string paymentDockId)
        {
            if (withdrawal == null) throw new ArgumentNullException(nameof(withdrawal));
            if (string.IsNullOrWhiteSpace(payoutId)) throw new ArgumentNullException(nameof(payoutId));
            if (string.IsNullOrWhiteSpace(paymentDockId)) throw new ArgumentNullException(nameof(paymentDockId));

            if (withdrawalPayoutConfirms.ContainsKey(withdrawal))
            {
                withdrawalPayoutConfirms[withdrawal] = new PayoutInfo(payoutId, paymentDockId);
            }
            else
            {
                throw new GameEngineException($"The withdrawal with ID {withdrawal.Id} is not being tracked for confirmation.");
            }
        }

        internal void AddWithdrawals(bool itIsThePresent, List<EnclosureWithdrawal> newEnclosureWithdrawals)
        {
            if (newEnclosureWithdrawals == null) throw new ArgumentNullException(nameof(newEnclosureWithdrawals));

            foreach (var enclosureWithdrawal in newEnclosureWithdrawals)
            {
                if (enclosureWithdrawal == null) throw new ArgumentNullException(nameof(enclosureWithdrawal));
                if (enclosureWithdrawal.Withdrawal == null) throw new ArgumentNullException(nameof(enclosureWithdrawal.Withdrawal));
                if (string.IsNullOrWhiteSpace(enclosureWithdrawal.PullPaymentId)) throw new ArgumentNullException(nameof(enclosureWithdrawal.PullPaymentId));
                if (withdrawalPayoutConfirms.ContainsKey(enclosureWithdrawal.Withdrawal)) throw new GameEngineException($"The withdrawal with ID {enclosureWithdrawal.Withdrawal.Id} is already being tracked for confirmation.");
            }

            foreach (var enclosureWithdrawal in newEnclosureWithdrawals)
            {
                withdrawalPayoutConfirms.TryAdd(enclosureWithdrawal.Withdrawal, null);
                if (itIsThePresent)
                {
                    var withdrawal = enclosureWithdrawal.Withdrawal;
                    PaymentManager.ClaimPullPayment(enclosureWithdrawal.PullPaymentId, withdrawal.Destination, withdrawal.Amount, Dispenser.Kind);
                }
            }
        }

        internal class PayoutInfo
        {
            internal string PayoutId { get; private set; }
            internal string PaymentStoreId { get; private set; }
            internal DateTime ConfirmedAt { get; private set; }
            internal PayoutInfo(string payoutId, string paymentStoreId)
            {
                if (string.IsNullOrWhiteSpace(payoutId)) throw new ArgumentNullException(nameof(payoutId));
                if (string.IsNullOrWhiteSpace(paymentStoreId)) throw new ArgumentNullException(nameof(paymentStoreId));
                PayoutId = payoutId;
                PaymentStoreId = paymentStoreId;
            }
        }
    }
}
