using ExternalServices;
using GamesEngine.Settings;
using GamesEngineMocks;
using LiquidityAPI.Controllers;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.OpenApi.Models;
using Puppeteer.EventSourcing;

namespace LiquidityAPI
{
    public class Startup
    {
        public Startup(IWebHostEnvironment env, IConfiguration configuration)
        {
            var builder = new ConfigurationBuilder()
                .SetBasePath(env.ContentRootPath)
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile("customization/appsettings.accounting.json", optional: true)
                .AddJsonFile("customization/appsettings.security.json", optional: true)
                .AddJsonFile("customization/appsettings.kafka.json", optional: true)
                .AddJsonFile("customization/appsettings.error_emails.json", optional: true)
                .AddJsonFile("customization/appsettings.apm.json", optional: true)
                .AddJsonFile("secrets/appsettings.secrets.json", optional: true);
            Configuration = builder.Build();
            Environment = env;
        }

        public IConfiguration Configuration { get; }
        public IWebHostEnvironment Environment { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddSingleton<RestAPIActorAsync>(provider => LiquidityAPI.Liquidity);
            services.AddControllers();
            services.AddMvc(options => options.EnableEndpointRouting = false);
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = "LiquidityAPI", Version = "v1" });
            });


            /*Security*/
            Security.Configure(services, Configuration);
            /*Security*/

            /*WebHookConfiguration*/
            WebHookClient.Configure(Configuration);
            /*WebHookConfiguration*/

            /*TownSettings*/
            TownSettings.Configure(Configuration);
            /*TownSettings*/

            /*InvoicePaymentSettings*/
            PaymentManager.Configure(Configuration);
            /*InvoicePaymentSettings*/

            services.AddMvc(options => options.EnableEndpointRouting = false).AddNewtonsoftJson();

            var biIntegration = Configuration.GetSection("BIIntegration");
            Console.WriteLine($"biIntegration {biIntegration}");

            Integration.Configure(KafkaMessage.Prefix.NoTransacction, biIntegration);
            Console.WriteLine($"Integration is ready.");

            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();

            ErrorsSender.Configure(Configuration.GetSection("ErrorsSender"), Environment.IsProduction());
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
                app.UseSwagger();
                app.UseSwaggerUI(c => { 
                    c.SwaggerEndpoint("/swagger/v1/swagger.json", "LiquidityAPI v1");
                    c.RoutePrefix = string.Empty;
                });
            }

            var sectionDairy = Configuration.GetSection("DBDairy");
            var mySQL = sectionDairy.GetSection("ConnectionStrings").GetValue<string>("MySQL");
            var sqlServer = sectionDairy.GetSection("ConnectionStrings").GetValue<string>("SQLServer");
            var dbSelected = sectionDairy.GetValue<string>("DBSelected");
            var scriptBeforeRecovering = Configuration.GetValue<string>("ActionsBeforeRecovering");

            DBDairy dbDairy = new DBDairy();
            dbDairy.DBSelected = dbSelected;
            Integration.DbDairy = dbDairy;

            if (dbSelected == DatabaseType.MySQL.ToString())
            {
                LiquidityAPI.Liquidity.EventSourcingStorage(DatabaseType.MySQL, mySQL, scriptBeforeRecovering, AccountingSettings.NeedsUniqueIdentifierForPaymentHub);
            }
            else if (dbSelected == DatabaseType.SQLServer.ToString())
            {
                LiquidityAPI.Liquidity.EventSourcingStorage(DatabaseType.SQLServer, sqlServer, scriptBeforeRecovering, AccountingSettings.NeedsUniqueIdentifierForPaymentHub);
            }
            else if (!String.IsNullOrWhiteSpace(dbSelected))
            {
                throw new Exception($"There is no connection for {dbSelected}");
            }
            else
            {
                //Integration.Kafka.OffSetResetToLatest();
                int numberOfTheMockConfigured = Convert.ToInt32(Configuration["MockToStart"]);

                var DOTNET_RUNNING_IN_CONTAINER = bool.Parse(System.Environment.GetEnvironmentVariable("DOTNET_RUNNING_IN_CONTAINER") ?? "false");
                if (DOTNET_RUNNING_IN_CONTAINER)
                    LiquidityAPI.Liquidity.EventSourcingStorage(DatabaseType.MySQL, mySQL, scriptBeforeRecovering, AccountingSettings.NeedsUniqueIdentifierForPaymentHub);

                RunMock(LiquidityAPI.Liquidity.Actor, numberOfTheMockConfigured);
            }

            if (Integration.UseKafka) new ConsumerMessaging().CreateConsumerForTopics();

            /*Security*/
            app.UseAuthentication();
            /*Security*/
            app.UseMvc();

            ForwardedHeadersOptions options = new ForwardedHeadersOptions();
            options.ForwardedHeaders = ForwardedHeaders.XForwardedFor;
            app.UseForwardedHeaders(options);

            //Start Sentinel Tasks
            var result = LiquidityAPI.Liquidity.PerformQry($@"
            {{
                for (liquids : company.System.LiquidFlow.Liquids)
                {{
                    liquid = liquids;
                    liquid.IngressSentinel.StartSentinelTasks(now);
                    liquid.EgressSentinel.StartSentinelTasks(now);
                }}
            }}
            ");
        }

        void RunMock(Puppeteer.EventSourcing.Actor actor, int index = -1)
        {
            Puppeteer.EventSourcing.ExecutionContext.Current.SetContext(DateTime.Now, true, actor);

            switch (index)
            {
                case 0:
                    LiquidityMocks.Init(actor);
                    break;
                case 1:
                    LiquidityMocks.SomeDepositsAndTanksWithOnly1Tanker(actor);
                    break;
                case 2:
                    LiquidityMocks.SomeWithdrawalsInInboxAndSomeDispensersWithWithdrawals(actor);
                    break;
                case 3:
                    LiquidityMocks.TankAndTankerWithMultipleVersions(actor);
                    break;
                case 4:
                    LiquidityMocks.DispenserWithMultipleVersions(actor);
                    break;
                default:
                    throw new Exception($"The mock {index} its not implemented yet.");
            }
        }
    }
}
