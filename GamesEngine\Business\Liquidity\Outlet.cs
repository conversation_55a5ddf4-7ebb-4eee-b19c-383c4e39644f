﻿using GamesEngine.Business.Liquidity.Containers;
using GamesEngine.Business.Liquidity.Transactions;
using GamesEngine.Domains;
using GamesEngine.RealTime;
using GamesEngine.RealTime.Events;
using GamesEngine.Settings;
using Puppeteer.EventSourcing.Libraries;
using System;
using System.Collections.Generic;
using System.Linq;
using static GamesEngine.Business.Liquidity.Containers.Bottle;
using static GamesEngine.Business.Liquidity.Containers.Dispenser;

namespace GamesEngine.Business.Liquidity
{
    internal class Outlet : Objeto
    {
        private readonly Dictionary<int, Dispenser> _dispensers = new();
        private readonly Dictionary<int, Dispenser> obsoleteDispensers = new();

        private readonly Dictionary<int, Bottle> _bottles = new();
        private readonly SlidingWindow<Transaction> transactionVelocityWindow;
        private const int MAXLRUCONTAINER = 8;
        private readonly LRUCache<int, Dispenser> _lruDispensers;

        private readonly HashSet<string> usedDestinationAddresses = new();

        public Outlet(Liquid liquid,DateTime now)
        {
            if (liquid == null) throw new ArgumentNullException(nameof(liquid), "Liquid cannot be null.");
            if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now));

            Liquid = liquid;
            DispenserInbox = new DispenserInbox(liquid.Kind, liquid, now);
            _lruDispensers = new LRUCache<int, Dispenser>(MAXLRUCONTAINER);
            AddOrUpdateDispenser(DispenserInbox);
            transactionVelocityWindow = new SlidingWindow<Transaction>(
               TimeSpan.FromHours(1),
               transaction => transaction.CreatedAt);

        }

        internal string Kind => Liquid.Kind;
        internal decimal Amount { get; private set; }
        internal decimal InAmount { get; private set; }

        internal Liquid Liquid { get; private set; }

        internal DispenserInbox DispenserInbox { get; private set; }

        internal IEnumerable<Dispenser> Dispensers => _dispensers.Values;

        internal IEnumerable<Dispenser> ObsoleteDispensers => obsoleteDispensers.Values;

        internal IEnumerable<Bottle> Bottles => _bottles.Values;

        internal DateTime MinDateDispenser
        {
            get
            {
                return CalculateMinDateDispenser();
            }
        }
        internal DateTime MaxDateDispenser
        {
            get
            {
                return CalculateMaxDateDispenser();
            }
        }

        private int withdrawalConsecutive = 0;
        internal int NextWithdrawalId()
        {
            return withdrawalConsecutive + 1;
        }

        internal IEnumerable<Dispenser> RootHierarchyDispensers(FilterContainerStatus filter)
        {
            return FilterDispenserByStatus(filter);
        }

        internal IEnumerable<Dispenser> FilterDispensersBy(FilterContainerStatus filter, string name = null, string color = null)
        {
            var filteredDispensers = FilterDispenserByStatus(filter);
            if(!string.IsNullOrWhiteSpace(name))
            {
                filteredDispensers = filteredDispensers.Where(d => d.Name.IndexOf(name, StringComparison.OrdinalIgnoreCase) >= 0);
            }
            if (!string.IsNullOrWhiteSpace(color))
            {
                filteredDispensers = filteredDispensers.Where(d => d.ContainerColor.IndexOf(color, StringComparison.OrdinalIgnoreCase) >= 0);
            }
            return filteredDispensers;
        }

        internal bool IsDispenserInUse(Dispenser dispenser)
        {
            if (dispenser == null) throw new ArgumentNullException(nameof(dispenser));

            var rootDispensers = _dispensers.Values.Where(d => d.Parent == null);
            foreach (var rootDispenser in rootDispensers)
            {
                if (rootDispenser is DispenserReady rootReadyDispenser)
                {
                    if (rootReadyDispenser == dispenser && rootReadyDispenser.HasBeenCommitted)
                    { 
                        return true;
                    }

                    if (rootReadyDispenser.ContainsRecursive(dispenser))
                    {
                        return rootReadyDispenser.HasBeenCommitted;
                    }
                }
            }
            return false;
        }

        internal bool ExistWithdrawal(int withdrawalId)
        {
            if (withdrawalId <= 0) throw new ArgumentNullException(nameof(withdrawalId));

            foreach (var dispenser in _dispensers.Values)
            {
                foreach (var withdrawal in dispenser.Withdrawals)//CAMBIAR POR UNO QUE NO BUSQEN EN LOS HIJOS
                {
                    if (withdrawal.Id == withdrawalId)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        internal bool ExistWithdrawalWithPullPaymentId(string pullPaymentId)
        {
            if (string.IsNullOrWhiteSpace(pullPaymentId)) throw new ArgumentNullException(nameof(pullPaymentId));

            foreach (var dispenser in _dispensers.Values)
            {
                foreach (var withdrawal in dispenser.EnclosureWithdrawals)
                {
                    if (withdrawal.PullPaymentId == pullPaymentId)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        internal Withdrawal FindWithdrawalFromPullPaymentId(string pullPaymentId)
        {
            if (string.IsNullOrWhiteSpace(pullPaymentId)) throw new ArgumentNullException(nameof(pullPaymentId));

            foreach (var dispenser in _dispensers.Values)
            {
                foreach (var withdrawal in dispenser.EnclosureWithdrawals)
                {
                    if (withdrawal.PullPaymentId == pullPaymentId)
                    {
                        return withdrawal.Withdrawal;
                    }
                }
            }
            throw new GameEngineException($"Withdrawal with pull payment id {pullPaymentId} not found.");
        }

        internal Withdrawal FindWithdrawal(int withdrawalId)
        {
            if (withdrawalId <= 0) throw new ArgumentNullException(nameof(withdrawalId));
            foreach (var dispenser in _dispensers.Values)
            {
                foreach (var withdrawal in dispenser.Withdrawals)
                {
                    if (withdrawal.Id == withdrawalId)
                    {
                        return withdrawal;
                    }
                }
            }
            throw new GameEngineException($"Withdrawal with ID {withdrawalId} not found.");
        }

        private int bottleConsecutive = 0;
        internal int NextBottleId()
        {
            return bottleConsecutive + 1;
        }

        private int dispenserConsecutive = 0;
        internal int NextDispenserId()
        {
            return dispenserConsecutive + 1;
        }

        internal Dispenser FindDispenser(int dispenserId)
        {
            if (dispenserId <= 0) throw new GameEngineException($"Dispenser ID must be greater than zero. Provided ID: {dispenserId}");
            if (_dispensers.TryGetValue(dispenserId, out Dispenser foundDispenser))
            {
                return foundDispenser;
            }

            if (obsoleteDispensers.TryGetValue(dispenserId, out Dispenser foundObsoleteDispenser))
            {
                return foundObsoleteDispenser;
            }
            throw new GameEngineException($"Dispenser with ID {dispenserId} not found.");
        }

        internal bool ExistWithdrawalInDispenser(int dispenserId, int withdrawalId)
        {
            if (dispenserId <= 0) throw new ArgumentNullException(nameof(dispenserId));
            if (withdrawalId <= 0) throw new ArgumentNullException(nameof(withdrawalId));
            if (_dispensers.TryGetValue(dispenserId, out Dispenser dispenser))
            {
                foreach (var withdrawal in dispenser.Withdrawals)
                {
                    if (withdrawal.Id == withdrawalId)
                    {
                        return true;
                    }
                }
                return false;
            }
            else
            {
                throw new GameEngineException($"Dispenser with ID {dispenserId} not found.");
            }
        }

        internal bool ExistDispenserFromWithdrawal(Withdrawal withdrawal)
        {
            if (withdrawal == null) throw new ArgumentNullException(nameof(withdrawal));
            foreach (var dispenser in _dispensers.Values)
            {
                foreach (var _withdrawal in dispenser.Withdrawals)
                {
                    if (_withdrawal == withdrawal)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        internal void AddLRUDispenserContainer(DispenserReady dispenser)
        {
            if (dispenser == null) throw new ArgumentNullException(nameof(dispenser));
            _lruDispensers.Put(dispenser.Id, dispenser);
        }

        internal IEnumerable<Dispenser> RecentDispensers()
        {
            List<Dispenser> dispensers = new List<Dispenser>();
            foreach (var dispenser in _lruDispensers.GetValues())
            {
                dispensers.Add(dispenser);
            }
            return dispensers;
        }

        internal Dispenser FindDispenserFromWithdrawal(Withdrawal withdrawal)
        {
            if (withdrawal == null) throw new ArgumentNullException(nameof(withdrawal));

            foreach (var dispenser in _dispensers.Values)
            {
                foreach (var _withdrawal in dispenser.Withdrawals)
                {
                    if (_withdrawal == withdrawal)
                    {
                        return dispenser;
                    }
                }
            }
            throw new GameEngineException($"Withdrawal with ID {withdrawal.Id} not found in any dispenser.");
        }

        internal DispenserReady CreateDispenser(bool itIsThePresent, DateTime createdDate, int id, string name, string description)
        {
            if (createdDate == DateTime.MinValue) throw new ArgumentNullException(nameof(createdDate));
            if (id <= 0) throw new ArgumentNullException(nameof(id));
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

            return CreateDispenser(itIsThePresent, createdDate, id, name, description, DateTime.MinValue, default(List<EnclosureWithdrawal>));
        }

        internal DispenserReady CreateDispenser(bool itIsThePresent, DateTime createdDate, int id, string name, string description, IEnumerable<int> witdrawalIds)
        {
            if (createdDate == DateTime.MinValue) throw new ArgumentNullException(nameof(createdDate));
            if (id <= 0) throw new ArgumentNullException(nameof(id));
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));

            if (witdrawalIds == null || !witdrawalIds.Any()) throw new GameEngineException("The list of withdrawal IDs cannot be null or empty.");
            List<EnclosureWithdrawal> enclosureWithdrawals = new List<EnclosureWithdrawal>();
            foreach (var withdrawalId in witdrawalIds)
            {
                if (!DispenserInbox.HasWithdrawal(withdrawalId)) throw new GameEngineException($"The withdrawal with ID {withdrawalId} does not exist.");
                var enclosureWithdrawal = DispenserInbox.FindEnclosureWithdrawal(withdrawalId);
                if (enclosureWithdrawal == null) throw new GameEngineException($"The enclosure for withdrawal with ID {withdrawalId} was not found.");
                if (enclosureWithdrawal.IsClaimed) throw new GameEngineException($"The withdrawal with ID {withdrawalId} is canceled and cannot be moved to the new dispenser.");
                enclosureWithdrawals.Add(enclosureWithdrawal);
            }

            return CreateDispenser(itIsThePresent, createdDate, id, name, description, DateTime.MinValue, enclosureWithdrawals);
        }

        internal DispenserReady CreateDispenser(bool itIsThePresent, DateTime createdDate, int id, string name, string description, DateTime startDate)
        {
            if (createdDate == DateTime.MinValue) throw new GameEngineException("The CreatedAt date is invalid.");
            if (id <= 0) throw new ArgumentNullException(nameof(id));
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException(nameof(name));
            if (startDate == DateTime.MinValue) throw new ArgumentNullException(nameof(startDate));
            if(startDate <= createdDate) throw new GameEngineException("The startDate cannot be earlier or equal than the created date.");

            return CreateDispenser(itIsThePresent,createdDate, id, name, description, startDate, default(List<EnclosureWithdrawal>));
        }
        internal DispenserReady CreateDispenser(bool itIsThePresent, DateTime createdDate, int id, string name, string description, DateTime startDate, IEnumerable<int> witdrawalIds)
        {
            if (createdDate == DateTime.MinValue) throw new GameEngineException("The CreatedAt date is invalid.");
            if (id <= 0) throw new ArgumentNullException(nameof(id));
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException("The Name is null or empty.");

            if (witdrawalIds == null || !witdrawalIds.Any()) throw new GameEngineException("The list of withdrawal IDs cannot be null or empty.");
            List<EnclosureWithdrawal> enclosureWithdrawals = new List<EnclosureWithdrawal>();
            foreach (var withdrawalId in witdrawalIds)
            {
                if (!DispenserInbox.HasWithdrawal(withdrawalId)) throw new GameEngineException($"The withdrawal with ID {withdrawalId} does not exist.");
                var enclosureWithdrawal = DispenserInbox.FindEnclosureWithdrawal(withdrawalId);
                if (enclosureWithdrawal == null) throw new GameEngineException($"The enclosure for withdrawal with ID {withdrawalId} was not found.");
                if (enclosureWithdrawal.IsClaimed) throw new GameEngineException($"The withdrawal with ID {withdrawalId} is canceled and cannot be moved to the new dispenser.");
                enclosureWithdrawals.Add(enclosureWithdrawal);
            }
            return CreateDispenser(itIsThePresent, createdDate, id, name, description, startDate, enclosureWithdrawals);
        }
        internal DispenserReady CreateDispenserWithDispenser(bool itIsThePresent, DateTime createdDate, int id, string name, string description, IEnumerable<int> dispenserIds)
        {
            if (createdDate == DateTime.MinValue) throw new GameEngineException("The CreatedAt date is invalid.");
            if (id <= 0) throw new ArgumentNullException(nameof(id));
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException("The Name is null or empty.");
            if (dispenserIds == null || !dispenserIds.Any()) throw new GameEngineException("The list of dispenser IDs cannot be null or empty.");
            List<Dispenser> dispensersToaAdd = new List<Dispenser>();
            foreach (var dispenserId in dispenserIds)
            {
                if (!ExistDispenser(dispenserId)) throw new GameEngineException($"The dispenser with ID {dispenserId} does not exist.");
                var dispenser = FindDispenser(dispenserId);
                if (dispenser == null) throw new ArgumentNullException(nameof(dispenser));
                if (dispenser is DispenserInbox) throw new GameEngineException($"The dispenser with ID {dispenserId} is an inbox and cannot be moved to the new dispenser.");
                if (dispenser.Parent != null) throw new GameEngineException($"The dispenser with ID {dispenserId} is not a root dispenser and cannot be moved to the new dispenser.");
                if (IsDispenserInUse(dispenser)) throw new GameEngineException($"The dispenser with ID {dispenserId} is in use and cannot be moved to the new dispenser.");
                dispensersToaAdd.Add(dispenser);
            }

            return CreateDispenserWithDispenser(itIsThePresent, createdDate, id, name, description, DateTime.MinValue, dispensersToaAdd);
        }

        internal DispenserReady CreateDispenserWithDispenser(bool itIsThePresent, DateTime createdDate, int id, string name, string description, DateTime startDate, IEnumerable<int> dispenserIds)
        {
            if (createdDate == DateTime.MinValue) throw new GameEngineException("The CreatedAt date is invalid.");
            if (id <= 0) throw new ArgumentNullException(nameof(id));
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentNullException("The Name is null or empty.");
            if (startDate == DateTime.MinValue) throw new ArgumentNullException(nameof(startDate));
            if (startDate <= createdDate) throw new GameEngineException("The startDate cannot be earlier or equal than the created date.");
            if (dispenserIds == null || !dispenserIds.Any()) throw new GameEngineException("The list of dispenser IDs cannot be null or empty.");
            List<Dispenser> dispensersToaAdd = new List<Dispenser>();
            foreach (var dispenserId in dispenserIds)
            {
                if (!ExistDispenser(dispenserId)) throw new GameEngineException($"The dispenser with ID {dispenserId} does not exist.");
                var dispenser = FindDispenser(dispenserId);
                if (dispenser == null) throw new ArgumentNullException(nameof(dispenser));
                if (dispenser is DispenserInbox) throw new GameEngineException($"The dispenser with ID {dispenserId} is an inbox and cannot be moved to the new dispenser.");
                if (dispenser.Parent != null) throw new GameEngineException($"The dispenser with ID {dispenserId} is not a root dispenser and cannot be moved to the new dispenser.");
                if (IsDispenserInUse(dispenser)) throw new GameEngineException($"The dispenser with ID {dispenserId} is in use and cannot be moved to the new dispenser.");
                dispensersToaAdd.Add(dispenser);
            }

            return CreateDispenserWithDispenser(itIsThePresent, createdDate, id, name, description, startDate,dispensersToaAdd);
        }

        
        private DispenserReady CreateDispenser(bool itIsThePresent, DateTime createdDate, int id, string name, string description, DateTime startDate, List<EnclosureWithdrawal> enclosureWithdrawals)
        {
            DispenserReady dispenser =  DispenserInbox.CreateDispenserWithWithdrawal(id, name, description, createdDate, startDate, enclosureWithdrawals);
            AddLRUDispenserContainer(dispenser);
            return dispenser;
        }

        private DispenserReady CreateDispenserWithDispenser(bool itIsThePresent, DateTime createdDate, int id, string name, string description, DateTime startDate, List<Dispenser> dispensers)
        {
            DispenserReady dispenser = DispenserInbox.CreateDispenserWithWithDispenser(id, name, description, createdDate, startDate, dispensers);
            AddLRUDispenserContainer(dispenser);
            return dispenser;
        }

        internal void AddOrUpdateDispenser(Dispenser dispenser)
        {
            if (dispenser == null) throw new ArgumentNullException(nameof(dispenser));

            if (dispenser is DispenserDiscarded dispenserDiscarded)
            {
                if (_dispensers.ContainsKey(dispenser.Id))
                {
                    _dispensers.Remove(dispenser.Id);
                    obsoleteDispensers.Add(dispenser.Id, dispenserDiscarded);
                }
                return;
            }

            if (_dispensers.TryGetValue(dispenser.Id, out Dispenser foundDispenser))
            {
                _dispensers[dispenser.Id] = dispenser;
            }
            else
            {
                _dispensers.Add(dispenser.Id, dispenser);
                if (!(dispenser is DispenserInbox))
                {
                    dispenserConsecutive = dispenser.Id;
                }
            }

            if (dispenser is DispenserInbox dispenserInbox) DispenserInbox = dispenserInbox;
        }

        internal Bottle FindBottle(int bottleId)
        {
            if (bottleId <= 0) throw new ArgumentException("Bottle ID must be greater than zero.", nameof(bottleId));
            if (_bottles.TryGetValue(bottleId, out Bottle foundBottle))
            {
                return foundBottle;
            }
            return null;
        }

        internal void AddOrUpdateBottle(Bottle bottle)
        {
            if (bottle == null) throw new ArgumentNullException(nameof(bottle));
            if (_bottles.TryGetValue(bottle.Id, out Bottle foundBottle))
            {
                _bottles[bottle.Id] = bottle;
            }
            else
            {
                _bottles.Add(bottle.Id, bottle);
                bottleConsecutive = bottle.Id;
            }
        }

        internal BottlePending CreateBottle(bool itIsThePresent, DateTime now, int bottleId, string name, DispenserReady dispenser)
        {
            if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now), "Date cannot be empty.");
            if (dispenser == null) throw new ArgumentNullException(nameof(dispenser));
            if (bottleId <= 0) throw new ArgumentException("Bottle ID must be greater than zero.", nameof(bottleId));
            if (string.IsNullOrWhiteSpace(name)) throw new ArgumentException("Name cannot be null or empty.", nameof(name));

            var bottle = new BottlePending(bottleId, name, Kind, Liquid, now);
            AddOrUpdateBottle(bottle);

            //if (Integration.UseKafka)
            //{
            //    using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, $"{Integration.Kafka.TopicForContainerEvents}"))
            //    {
            //        CreatedBottleMessage createdDispenserMessage = new CreatedBottleMessage(bottleId, name, Kind, now);
            //        buffer.Send(createdDispenserMessage);
            //    }
            //}

            if (itIsThePresent)
            {
                CreatedBottleEvent createdBottleEvent = new CreatedBottleEvent(now, bottle.Id, bottle.Name);
                PlatformMonitor.GetInstance().WhenNewEvent(createdBottleEvent);
            }

            return bottle;
        }

        internal bool ExistDispenser(int dispenserId)
        {
            if (dispenserId <= 0) throw new ArgumentException("Dispenser ID must be greater than zero.", nameof(dispenserId));
            var result = _dispensers.ContainsKey(dispenserId);
            if (!result) result = obsoleteDispensers.ContainsKey(dispenserId);
            return result;
        }

        internal bool IsDispenserObsolete(int dispenserId)
        {
            if (dispenserId <= 0) throw new ArgumentException("Dispenser ID must be greater than zero.", nameof(dispenserId));
            return obsoleteDispensers.ContainsKey(dispenserId);
        }

        internal bool DestinationAddressHasBeenUsed(string destinationAddress)
        {
            if (string.IsNullOrWhiteSpace(destinationAddress)) throw new ArgumentNullException(nameof(destinationAddress));
            return usedDestinationAddresses.Contains(destinationAddress);
        }

        internal Withdrawal CreateWithdrawal(bool itIsThePresent, DateTime now, int withdrawalId, int authorization, decimal amount, string destionation, string atAddress, Domain domain, int storeId, string externalReference, decimal rate, decimal receivedAmount, string receivedCurrency)
        {
            if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now));
            if (withdrawalId <= 0) throw new ArgumentNullException(nameof(withdrawalId));
            if (authorization <= 0) throw new ArgumentNullException(nameof(authorization));
            if (amount <= 0) throw new ArgumentNullException(nameof(amount));
            if (receivedAmount <= 0) throw new ArgumentNullException(nameof(receivedAmount));
            if (rate <= 0) throw new ArgumentNullException(nameof(rate));
            if (string.IsNullOrWhiteSpace(destionation)) throw new ArgumentNullException(nameof(destionation));
            if (string.IsNullOrWhiteSpace(atAddress)) throw new ArgumentNullException(nameof(atAddress));
            if (domain == null) throw new ArgumentNullException(nameof(domain));
            if (storeId == 0) throw new ArgumentNullException(nameof(storeId));
            if (string.IsNullOrWhiteSpace(externalReference)) throw new ArgumentNullException(nameof(externalReference));
            if (string.IsNullOrWhiteSpace(receivedCurrency)) throw new ArgumentNullException(nameof(receivedCurrency));

            if (usedDestinationAddresses.Contains(destionation)) throw new GameEngineException($"The destination address '{destionation}' has already been used for another withdrawal in this outlet. Each withdrawal must have a unique destination address.");

            var withdrawal = DispenserInbox.CreateWithdrawal(now, withdrawalId, authorization, amount, destionation, atAddress, domain, storeId, externalReference,rate, receivedAmount, receivedCurrency);
            withdrawalConsecutive = withdrawalId;
            usedDestinationAddresses.Add(destionation);

            if (itIsThePresent)
            {
                if (Integration.UseKafka)
                {
                    using (KafkaMessagesBuffer buffer = new KafkaMessagesBuffer(itIsThePresent, Integration.Kafka.TopicForContainerEvents))
                    {
                        var createdWithdrawalMessage = new CreatedWithdrawalMessage(this.Kind, withdrawal.Id, withdrawal.Amount, withdrawal.CreatedAt, withdrawal.Destination, withdrawal.AtAddress, withdrawal.ExternalReference, withdrawal.Reference, domain.Id, withdrawal.Rate);
                        buffer.Send(createdWithdrawalMessage);
                    }
                }
                //CreatedWithdrawalEvent createdWithdrawalEvent = new CreatedWithdrawalEvent(now, withdrawal.Id, withdrawal.Amount, withdrawal.Destination);
                //PlatformMonitor.GetInstance().WhenNewEvent(createdWithdrawalEvent);
            }

            return withdrawal;
        }

        internal void CancelWithdrawal(bool itIsThePresent, DateTime now, int withdrawalId)
        {
            if (now == DateTime.MinValue) throw new ArgumentNullException(nameof(now), "Date cannot be empty.");
            if (withdrawalId <= 0) throw new ArgumentNullException(nameof(withdrawalId));

            DispenserInbox.CancelWithdrawal(now, withdrawalId);
            withdrawalConsecutive = withdrawalId;
        }

        internal IEnumerable<Dispenser> FindDispensersBy(string nameFilter)
        {
            var result = _dispensers.Values.Where(d => !(d is Dispenser.DispenserInbox));

            if (!string.IsNullOrWhiteSpace(nameFilter))
            {
                result = result.Where(d => d.Name.IndexOf(nameFilter, StringComparison.OrdinalIgnoreCase) >= 0);
            }

            return result.ToList();
        }

        private IEnumerable<Dispenser> OrderByCreatedDate(IEnumerable<Dispenser> dispensers)
        {
            if(dispensers == null) throw new ArgumentNullException(nameof(dispensers));
            return dispensers.OrderByDescending(d => d.CreatedAt);
        }

        internal IEnumerable<Dispenser> FilterDispenserByStatus(FilterContainerStatus status)
        {
            switch (status)
            {
                case FilterContainerStatus.READY:
                    return OrderByCreatedDate(_dispensers.Values.Where(c => c.Parent == null && c is DispenserReady));
                case FilterContainerStatus.DISCARDED:
                    return OrderByCreatedDate(_dispensers.Values.Where(c => c.Parent == null && c is DispenserDiscarded));
                default:
                    return OrderByCreatedDate(_dispensers.Values.Where(c => c.Parent == null && c  is DispenserReady));
            }
        }

        internal List<Dispenser> BuildDispenserWithWithdrawalsBetween(DateTime from, DateTime to, FilterContainerStatus filter, string name = null, string color = null)
        {
            if (from == DateTime.MinValue && to == DateTime.MinValue) throw new ArgumentNullException("Both 'from' and 'to' dates cannot be MinValue.");

            var filteredDispensers = FilterDispenserByStatus(filter);
            if(!string.IsNullOrWhiteSpace(name))
            {
                filteredDispensers = filteredDispensers.Where(d => d.Name.IndexOf(name, StringComparison.OrdinalIgnoreCase) >= 0);
            }
            if(!string.IsNullOrWhiteSpace(color))
            {
                filteredDispensers = filteredDispensers.Where(d => d.ContainerColor.IndexOf(color, StringComparison.OrdinalIgnoreCase) >= 0);
            }
            List<Dispenser> dispenserList = new List<Dispenser>();
            foreach (var dispenser in filteredDispensers)
            {

                var hasWithdrawal = false;
                if (from != DateTime.MinValue && to != DateTime.MinValue)
                {
                    if (from > to) throw new ArgumentException("The 'from' date cannot be greater than the 'to' date.", nameof(from));

                    hasWithdrawal = dispenser.BuildWithdrawalsBetween(from, to);
                }
                else if (from != DateTime.MinValue)
                {
                    hasWithdrawal = dispenser.BuildWithdrawalsFrom(from);
                }
                else if (to != DateTime.MinValue)
                {
                    hasWithdrawal = dispenser.BuildWithdrawalsUpTo(to);
                }

                if (hasWithdrawal)
                {
                    dispenserList.Add(dispenser);
                }
            }
            return dispenserList;
        }

        internal List<Dispenser> BuildDispenserWithWithdrawalsFrom(DateTime from, FilterContainerStatus filter, string name = null, string color = null)
        {
            if (from == DateTime.MinValue) throw new ArgumentNullException(nameof(from));
            List<Dispenser> tankerList = new List<Dispenser>();

            return BuildDispenserWithWithdrawalsBetween(from, DateTime.MinValue, filter, name, color);

        }

        internal List<Dispenser> BuildDispenserWithWithdrawalsUpTo(DateTime to, FilterContainerStatus filter, string name = null, string color = null)
        {
            if (to == DateTime.MinValue) throw new ArgumentNullException(nameof(to));
            List<Dispenser> tankerList = new List<Dispenser>();

            return BuildDispenserWithWithdrawalsBetween(DateTime.MinValue, to, filter, name, color);
        }

        private DateTime CalculateMinDateDispenser()
        {
            if (!_dispensers.Any())
                return DateTime.MaxValue;

            DateTime minDate = DateTime.MaxValue;
            foreach (var dispenser in _dispensers.Values)
            {

                if (dispenser.MinDate < minDate)
                {
                    minDate = dispenser.MinDate;
                }
            }
            return minDate;
        }

        private DateTime CalculateMaxDateDispenser()
        {
            if (!_dispensers.Any())
                return DateTime.MinValue;

            DateTime maxDate = DateTime.MinValue;
            foreach (var dispenser in _dispensers.Values)
            {
                if (dispenser.MaxDate > maxDate)
                {
                    maxDate = dispenser.MaxDate;
                }
            }
            return maxDate;
        }

        internal void AddConfirmedWithdrawalToSliding(DateTime now,Withdrawal withdrawal)
        {
            if (withdrawal == null) throw new ArgumentNullException(nameof(withdrawal));
            transactionVelocityWindow.Add(withdrawal, now);
        }

        internal decimal CalculateExchangeAmount(decimal rate, decimal amount)
        {
            if (rate <= 0) throw new GameEngineException("Exchange rate must be greater than zero.");
            if (amount <= 0) throw new GameEngineException("Amount must be greater than zero.");
            return amount / rate;
        }


    }
}
