﻿using GamesEngine.Business.Liquidity.ExternalServices;
using Microsoft.Extensions.Configuration;
using Puppeteer.EventSourcing.Libraries;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace GamesEngine.Settings
{
    public class PaymentManager
    {
        public static void Configure(IConfiguration configuration)
        {
            var setup = configuration.GetSection("InvoicePayment");
            if (setup == null) throw new ArgumentNullException(nameof(setup), "InvoicePayment settings section is missing in the configuration.");

            NodeExplorerUrl = setup["NodeExplorerUrl"] ?? throw new ArgumentNullException(nameof(NodeExplorerUrl), "NodeExplorerUrl is not configured.");
            if (string.IsNullOrEmpty(NodeExplorerUrl)) throw new ArgumentNullException(nameof(NodeExplorerUrl), "NodeExplorerUrl cannot be null or empty.");

            NodeExplorerClient = new NodeExplorerClient(NodeExplorerUrl);
            PublicNodeExplorerClient = new PublicNodeExplorerClient();

            NodoUrl = setup["NodoUrl"] ?? throw new ArgumentNullException(nameof(NodoUrl), "NodoUrl is not configured.");
            NodoUser = setup["NodoUser"] ?? throw new ArgumentNullException(nameof(NodoUser), "NodoUser is not configured.");
            NodoPassword = setup["NodoPassword"] ?? throw new ArgumentNullException(nameof(NodoPassword), "NodoPassword is not configured.");

            PaymentGatewayUrl = setup["PaymentGatewayUrl"] ?? throw new ArgumentNullException(nameof(PaymentGatewayUrl), "PaymentGatewayUrl is not configured.");

            Store = setup["Store"] ?? throw new ArgumentNullException(nameof(Store), "Store is not configured.");
            Token = setup["Token"] ?? throw new ArgumentNullException(nameof(Token), "Token is not configured.");
            WebhookSecret = setup["WebhookSecret"] ?? throw new ArgumentNullException(nameof(WebhookSecret), "WebhookSecret is not configured.");

            StoreForAutoWithdrawal = setup["StoreForAutoWithdrawal"] ?? throw new ArgumentNullException(nameof(StoreForAutoWithdrawal), "StoreForAutoWithdrawal is not configured.");
            TokenForAutoWithdrawal = setup["TokenForAutoWithdrawal"] ?? throw new ArgumentNullException(nameof(TokenForAutoWithdrawal), "TokenForAutoWithdrawal is not configured.");
            WebhookSecretForAutoWithdrawal = setup["WebhookSecretForAutoWithdrawal"] ?? throw new ArgumentNullException(nameof(WebhookSecretForAutoWithdrawal), "WebhookSecretForAutoWithdrawal is not configured.");

            IssuerMail = setup["IssuerMail"] ?? throw new ArgumentNullException(nameof(IssuerMail), "IssuerMail is not configured.");
        }

        public static string NodeExplorerUrl { get; set; }
        public static NodeExplorerClient NodeExplorerClient { get; set; }
        public static PublicNodeExplorerClient PublicNodeExplorerClient { get; set; }

        public static string NodoUrl { get; set; }
        public static string NodoUser { get; set; }
        public static string NodoPassword { get; set; }

        public static string PaymentGatewayUrl { get; private set; }

        public static string Store { get; private set; }
        public static string Token { get; private set; }
        public static string WebhookSecret { get; private set; }

        public static string StoreForAutoWithdrawal { get; private set; }
        public static string TokenForAutoWithdrawal { get; private set; }
        public static string WebhookSecretForAutoWithdrawal { get; private set; }

        public static string IssuerMail { get; private set; }

        public static async Task<PaymentInvoiceResponse> InvoicePaymentMethodAsync(string invoiceId)
        {
            if (string.IsNullOrEmpty(invoiceId)) throw new ArgumentNullException(nameof(invoiceId), "Invoice ID cannot be null or empty.");

            var invoiceUrl = $"{PaymentGatewayUrl}/api/v1/stores/{Store}/invoices/{invoiceId}/payment-methods";

            var httpClient = new HttpClient();
            var request = new HttpRequestMessage(HttpMethod.Get, invoiceUrl);
            request.Headers.Add("Authorization", $"token {Token}");

            var response = await httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException($"Failed to retrieve payment methods for invoice {invoiceId}: {response.ReasonPhrase}");
            }
            var responseContent = await response.Content.ReadAsStringAsync();
            var options = new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true };
            // Para deserializar un array de PaymentInvoiceResponse:
            var paymentInvoiceResponses = System.Text.Json.JsonSerializer.Deserialize<List<PaymentInvoiceResponse>>(responseContent, options);
            if (paymentInvoiceResponses == null) throw new InvalidOperationException("Failed to deserialize invoice response.");

            if (paymentInvoiceResponses.Count == 0) throw new InvalidOperationException($"No payment methods found for invoice {invoiceId}.");
            return paymentInvoiceResponses.FirstOrDefault();
        }

        public static async Task<PaymentMethodBalanceResponse> PaymentMethodBalanceAsync(string paymentMethodCurrency, string storePaymentDockId = "")
        {
            if (string.IsNullOrEmpty(paymentMethodCurrency)) throw new ArgumentNullException(nameof(paymentMethodCurrency), "Payment method currency cannot be null or empty.");

            bool useAutoStore = storePaymentDockId == StoreForAutoWithdrawal;
            string storeId = useAutoStore ? StoreForAutoWithdrawal : Store;
            var balanceUrl = $"{PaymentGatewayUrl}/api/v1/stores/{storeId}/payment-methods/{paymentMethodCurrency}-ONCHAIN/wallet";
            var httpClient = new HttpClient();
            var request = new HttpRequestMessage(HttpMethod.Get, balanceUrl);

            string token = useAutoStore ? TokenForAutoWithdrawal : Token;
            request.Headers.Add("Authorization", $"token {token}");

            var response = await httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException($"Failed to retrieve balance for payment method {paymentMethodCurrency}-ONCHAIN: {response.ReasonPhrase}");
            }
            var responseContent = await response.Content.ReadAsStringAsync();
            var options = new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true };
            var balanceResponse = System.Text.Json.JsonSerializer.Deserialize<PaymentMethodBalanceResponse>(responseContent, options);
            if (balanceResponse == null) throw new InvalidOperationException("Failed to deserialize balance response.");
            return balanceResponse;
        }

        public class PaymentMethodBalanceResponse
        {
            public string Balance { get; set; }
            public string UnconfirmedBalance { get; set; }
            public string ConfirmedBalance { get; set; }
            public string Label { get; set; }
        }

        public static async Task<InvoiceResponse> CreateInvoiceAsync(string currency, decimal amount, int orderId, string orderDesc)
        {
            if (string.IsNullOrEmpty(currency)) throw new ArgumentNullException(nameof(currency), "Currency cannot be null or empty.");

            var invoiceUrl = $"{PaymentGatewayUrl}/api/v1/stores/{Store}/invoices";

            var httpClient = new HttpClient();
            var request = new HttpRequestMessage(HttpMethod.Post, invoiceUrl);

            request.Headers.Add("Authorization", $"Bearer {Token}");

            var invoiceContent = System.Text.Json.JsonSerializer.Serialize(new
            {
                amount,
                currency,
                metadata = new
                {
                    IssuerMail,
                    orderId,
                    orderDesc
                },
                checkout = new
                {
                    speedPolicy = "HighSpeed"
                }
            });

            request.Content = new StringContent(invoiceContent, Encoding.UTF8, "application/json");
            var response = await httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException($"Failed to create invoice: {response.ReasonPhrase}");
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            var options = new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true };
            var invoiceResponse = System.Text.Json.JsonSerializer.Deserialize<InvoiceResponse>(responseContent, options);
            if (invoiceResponse == null)
            {
                throw new InvalidOperationException("Failed to deserialize invoice response.");
            }

            return invoiceResponse;
        }

        internal static async Task<int> TotalConfirmatiosAsync(PaymentInvoice invoice)
        {
            if (invoice == null) throw new ArgumentNullException(nameof(invoice));
            int slipIndex = invoice.PaymentId.IndexOf('-');
            string paymentId = invoice.PaymentId.Substring(0, slipIndex);
            if (string.IsNullOrEmpty(paymentId)) throw new ArgumentNullException(nameof(paymentId));

            return await TotalConfirmatiosAsync(paymentId);
        }

        internal static async Task<int> TotalConfirmatiosAsync(string paymentId)
        {
            if (string.IsNullOrEmpty(paymentId)) throw new ArgumentNullException(nameof(paymentId));

            var nodoUrl = $"{NodoUrl}/";
            var httpClient = new HttpClient();
            
            var byteArray = Encoding.ASCII.GetBytes($"{NodoUser}:{NodoPassword}");
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", Convert.ToBase64String(byteArray));
            
            var request = new HttpRequestMessage(HttpMethod.Post, nodoUrl);
            var jsonBody = $@"{{
                ""jsonrpc"": ""1.0"",
                ""id"": ""akai-check"",
                ""method"": ""getrawtransaction"",
                ""params"": [""{paymentId}"", true]
            }}";

            var content = new StringContent(jsonBody, Encoding.UTF8, "text/plain");
            var response = await httpClient.PostAsync(nodoUrl, content);
            if (!response.IsSuccessStatusCode)
            {
                throw new GameEngineException($"Failed to get transaction details: {response.ReasonPhrase} in {nodoUrl}");
            }
            var responseContent = await response.Content.ReadAsStringAsync();
            var options = new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true };
            var transactionDetails = System.Text.Json.JsonSerializer.Deserialize<RawTransactionResponse>(responseContent, options);
            if (transactionDetails == null)
            {
                throw new GameEngineException("Failed to deserialize transaction details or confirmations are missing.");
            }
            var result = transactionDetails.Result.Confirmations;
            return result;
        }

        public class ExchangeRateResponse
        {
            public string CryptoCode { get; set; }
            public decimal Rate { get; set; }
        }

        internal static async Task<ValidAddressResponse> ValidAddressAsync(string address)
        {
            if (string.IsNullOrEmpty(address)) throw new ArgumentNullException(nameof(address));

            var nodoUrl = $"{NodoUrl}/";
            var httpClient = new HttpClient();

            var byteArray = Encoding.ASCII.GetBytes($"{NodoUser}:{NodoPassword}");
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", Convert.ToBase64String(byteArray));

            var request = new HttpRequestMessage(HttpMethod.Post, nodoUrl);
            var jsonBody = $@"{{
                ""jsonrpc"": ""1.0"",
                ""id"": ""validate_test"",
                ""method"": ""validateaddress"",
                ""params"": [""{address}""]
            }}";

            var content = new StringContent(jsonBody, Encoding.UTF8, "application/json");
            var response = await httpClient.PostAsync(nodoUrl, content);
            if (!response.IsSuccessStatusCode)
            {
                throw new GameEngineException($"Failed to check valid address: {response.ReasonPhrase} in {nodoUrl}");
            }
            var responseContent = await response.Content.ReadAsStringAsync();
            var options = new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true };
            var transactionDetails = System.Text.Json.JsonSerializer.Deserialize<ValidAddressResponse>(responseContent, options);
            if (transactionDetails == null)
            {
                throw new GameEngineException("Failed to deserialize transaction details or confirmations are missing.");
            }
            return transactionDetails;
        }

        public class ValidAddressResponse
        {
            public ValidAddressResult Result { get; set; }
            public string Error { get; set; }
            public string Id { get; set; }

            public class ValidAddressResult
            {
                public bool Isvalid { get; set; }
                public string Address { get; set; }
                public string ScriptPubKey { get; set; }
                public bool Isscript { get; set; }
                public bool Iswitness { get; set; }
                public int Witness_version { get; set; }
                public string Witness_program { get; set; }
            }
        }

        public static async Task<ExchangeRateResponse> ExchangeRateAsync(string kind, string referenceKind)
        {
            if (string.IsNullOrEmpty(kind)) throw new ArgumentNullException(nameof(kind), "Kind cannot be null or empty.");
            if (string.IsNullOrEmpty(referenceKind)) throw new ArgumentNullException(nameof(referenceKind), "Reference kind cannot be null or empty.");
            
            var url = $"{PaymentGatewayUrl}/api/rates?storeId={Store}&currencyPairs={kind}_{referenceKind}";

            using var httpClient = new HttpClient();
            var request = new HttpRequestMessage(HttpMethod.Get, url);

            request.Headers.Add("Authorization", $"token {Token}");

            var response = await httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException($"Failed to retrieve exchange rate: {response.ReasonPhrase}");
            }
            var content = await response.Content.ReadAsStringAsync();
            var options = new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true };
            var rates = System.Text.Json.JsonSerializer.Deserialize<List<ExchangeRateResponse>>(content, options);
            if (rates == null || !rates.Any())
            {
                throw new Exception($"No exchange rate found for {kind} to {referenceKind}.");
            }
            var rate = rates.FirstOrDefault();
            if (rate == null) throw new Exception($"Failed to retrieve exchange rate for {kind} to {referenceKind}.");
            return rate;
        }

        internal static async Task ArchivePullPaymentAsync(string pullPaymentId, string storePaymentDockId = "")
        {
            if (string.IsNullOrEmpty(pullPaymentId)) throw new ArgumentNullException(nameof(pullPaymentId), "Pull payment ID cannot be null or empty.");

            bool useAutoStore = storePaymentDockId == StoreForAutoWithdrawal;

            string storeId = useAutoStore ? StoreForAutoWithdrawal : Store;
            var url = $"{PaymentGatewayUrl}/api/v1/stores/{storeId}/pull-payments/{pullPaymentId}";

            using (var httpClient = new HttpClient())
            {
                var request = new HttpRequestMessage(HttpMethod.Delete, url);

                string token = useAutoStore ? TokenForAutoWithdrawal : Token;
                request.Headers.Add("Authorization", $"Bearer {token}");

                var response = await httpClient.SendAsync(request);
                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    throw new Exception($"Failed to archive pull payment: {response.ReasonPhrase}. Error: {errorContent}");
                }
            }
        }

        private static RestClient _postUpdateWagersClient;
        internal static void ArchivePullPayment(string pullPaymentId, string storePaymentDockId = "")
        {
            if (string.IsNullOrEmpty(pullPaymentId)) throw new ArgumentNullException(nameof(pullPaymentId), "Pull payment ID cannot be null or empty.");

            bool useAutoStore = storePaymentDockId == StoreForAutoWithdrawal;
            string storeId = useAutoStore ? StoreForAutoWithdrawal : Store;
            var url = $"{PaymentGatewayUrl}/api/v1/stores/{storeId}/pull-payments/{pullPaymentId}";

            var request = new RestRequest(url, Method.Delete);

            string token = useAutoStore ? TokenForAutoWithdrawal : Token;
            request.AddHeader("Authorization", $"Bearer {token}");

            if (_postUpdateWagersClient == null) _postUpdateWagersClient = new RestClient();
            var response = _postUpdateWagersClient.Execute(request);

            if (response.StatusCode != HttpStatusCode.OK)
            {
                throw new Exception("Failed to archive pull payment: " + response.Content);
            }
        }

        internal static PullPaymentResponse CreatePullPayment(string name, decimal amount, string kind, string storePaymentDockId = "")
        {
            if (string.IsNullOrEmpty(name)) throw new ArgumentNullException(nameof(name), "Name cannot be null or empty.");
            if (amount <= 0) throw new ArgumentNullException(nameof(amount), "Amount must be greater than zero.");
            if (string.IsNullOrEmpty(kind)) throw new ArgumentNullException(nameof(kind), "Kind cannot be null or empty.");

            bool useAutoStore = storePaymentDockId == StoreForAutoWithdrawal;

            string storeId = useAutoStore ? StoreForAutoWithdrawal : Store;
            var url = $"{PaymentGatewayUrl}/api/v1/stores/{storeId}/pull-payments";
            var request = new RestRequest(url, Method.Post);
            string token = useAutoStore ? TokenForAutoWithdrawal : Token;
            request.AddHeader("Authorization", $"Bearer {token}");
            var requestBody = new
            {
                name,
                amount = amount.ToString(CultureInfo.InvariantCulture),
                //currency,
                currency = kind,
                autoApproveClaims = true,
                paymentMethods = new List<string> { $"{kind}-CHAIN" }
            };
            request.AddJsonBody(requestBody);
            if (_postUpdateWagersClient == null) _postUpdateWagersClient = new RestClient();
            var response = _postUpdateWagersClient.Execute(request);
            if (response.StatusCode != HttpStatusCode.OK && response.StatusCode != HttpStatusCode.Created)
            {
                throw new Exception($"Failed to create pull payment: {response.Content}");
            }
            var options = new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true };
            var pullPaymentResponse = System.Text.Json.JsonSerializer.Deserialize<PullPaymentResponse>(response.Content, options);
            if (pullPaymentResponse == null) throw new Exception("Failed to deserialize pull payment response.");
            return pullPaymentResponse;
        }

        internal static async Task<PullPaymentResponse> CreatePullPaymentAsync(string name, decimal amount, string kind, string storePaymentDockId = "")
        {
            if (string.IsNullOrEmpty(name)) throw new ArgumentNullException(nameof(name), "Name cannot be null or empty.");
            if (amount <= 0) throw new ArgumentOutOfRangeException(nameof(amount), "Amount must be greater than zero.");
            if (string.IsNullOrEmpty(kind)) throw new ArgumentNullException(nameof(kind), "Kind cannot be null or empty.");

            bool useAutoStore = storePaymentDockId == StoreForAutoWithdrawal;
            string storeId = useAutoStore ? StoreForAutoWithdrawal : Store;
            var url = $"{PaymentGatewayUrl}/api/v1/stores/{storeId}/pull-payments";

            using var httpClient = new HttpClient();
            var request = new HttpRequestMessage(HttpMethod.Post, url);

            string token = useAutoStore ? TokenForAutoWithdrawal : Token;
            request.Headers.Add("Authorization", $"Bearer {token}");

            var requestBody = new
            {
                name,
                amount = amount.ToString(CultureInfo.InvariantCulture),
                //currency,
                currency = kind,
                autoApproveClaims = true,
                paymentMethods = new List<string> { $"{kind}-CHAIN" }
            };

            request.Content = new StringContent(JsonSerializer.Serialize(requestBody), Encoding.UTF8, "application/json");
            var response = await httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                throw new Exception($"Failed to create pull payment: {response.ReasonPhrase}. Error: {errorContent}");
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            var options = new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true };
            var pullPaymentResponse = System.Text.Json.JsonSerializer.Deserialize<PullPaymentResponse>(responseContent, options);
            if (pullPaymentResponse == null) throw new Exception("Failed to deserialize pull payment response.");

            return pullPaymentResponse;
        }

        internal static ClaimPullPaymentResponse ClaimPullPayment(string pullPaymentId, string destination, decimal amount, string kind)
        {
            if (string.IsNullOrEmpty(pullPaymentId)) throw new ArgumentNullException(nameof(pullPaymentId), "Pull payment ID cannot be null or empty.");
            if (string.IsNullOrEmpty(destination)) throw new ArgumentNullException(nameof(destination), "Destination cannot be null or empty.");
            if (amount <= 0) throw new ArgumentOutOfRangeException(nameof(amount), "Amount must be greater than zero.");
            if (string.IsNullOrEmpty(kind)) throw new ArgumentNullException(nameof(kind), "Kind cannot be null or empty.");
            var url = $"{PaymentGatewayUrl}/api/v1/pull-payments/{pullPaymentId}/payouts";
            var request = new RestRequest(url, Method.Post);
            request.AddHeader("Authorization", $"Bearer {TokenForAutoWithdrawal}");
            var requestBody = new
            {
                destination,
                amount = amount.ToString(CultureInfo.InvariantCulture),
                payoutMethodId = $"{kind}-CHAIN",
            };
            request.AddJsonBody(requestBody);
            if (_postUpdateWagersClient == null) _postUpdateWagersClient = new RestClient();
            var response = _postUpdateWagersClient.Execute(request);
            if (response.StatusCode != HttpStatusCode.OK && response.StatusCode != HttpStatusCode.Created)
            {
                throw new Exception($"Failed to claim pull payment: {response.Content}");
            }
            var options = new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true };
            var claimPullPaymentResponse = System.Text.Json.JsonSerializer.Deserialize<ClaimPullPaymentResponse>(response.Content, options);
            if (claimPullPaymentResponse == null) throw new Exception("Failed to deserialize claim pull payment response.");
            return claimPullPaymentResponse;
        }

        internal static async Task<ClaimPullPaymentResponse> ClaimPullPaymentAsync(string pullPaymentId, string destination, decimal amount, string kind)
        {
            if (string.IsNullOrEmpty(pullPaymentId)) throw new ArgumentNullException(nameof(pullPaymentId), "Pull payment ID cannot be null or empty.");
            if (string.IsNullOrEmpty(destination)) throw new ArgumentNullException(nameof(destination), "Destination cannot be null or empty.");
            if (amount <= 0) throw new ArgumentOutOfRangeException(nameof(amount), "Amount must be greater than zero.");
            if (string.IsNullOrEmpty(kind)) throw new ArgumentNullException(nameof(kind), "Kind cannot be null or empty.");

            var url = $"{PaymentGatewayUrl}/api/v1/pull-payments/{pullPaymentId}/payouts";
            using var httpClient = new HttpClient();
            var request = new HttpRequestMessage(HttpMethod.Post, url);
            request.Headers.Add("Authorization", $"Bearer {TokenForAutoWithdrawal}");

            var requestBody = new
            {
                destination,
                amount = amount.ToString(CultureInfo.InvariantCulture),
                payoutMethodId = $"{kind}-CHAIN",
            };

            request.Content = new StringContent(JsonSerializer.Serialize(requestBody), Encoding.UTF8, "application/json");
            var response = await httpClient.SendAsync(request);
            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                throw new Exception($"Failed to claim pull payment: {response.ReasonPhrase}. Error: {errorContent}");
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            var options = new System.Text.Json.JsonSerializerOptions { PropertyNameCaseInsensitive = true };
            var claimPullPaymentResponse = System.Text.Json.JsonSerializer.Deserialize<ClaimPullPaymentResponse>(responseContent, options);
            if (claimPullPaymentResponse == null) throw new Exception("Failed to deserialize claim pull payment response.");

            return claimPullPaymentResponse;
        }

        public class ClaimPullPaymentResponse
        {
            public string Id { get; set; }
        }

        public class PullPaymentResponse
        {
            public string Id { get; set; }
        }

        public class InvoiceResponse
        {
            public string Id { get; set; }
        }
        public class PaymentInvoiceResponse
        {
            public string Destination { get; set; }
            public string PaymentLink { get; set; }
            public string Rate { get; set; }
            public string Amount { get; set; }
            public string TotalPaid { get; set; }
            public string Due { get; set; }
            public string Currency { get; set; }
            public List<Payment> Payments { get; set; }
            public class Payment
            {
                public string Id { get; set; }
                public long ReceivedDate { get; set; }
                public string Value { get; set; }
                public string Fee { get; set; }
                public string Status { get; set; }
                public string Destination { get; set; }
            }
        }

        internal class RawTransactionResponse
        {
            public RawTransactionResult Result { get; set; }
            public object Error { get; set; }
            public string Id { get; set; }
        }

        internal class RawTransactionResult
        {
            public string Txid { get; set; }
            public string Hash { get; set; }
            public int Version { get; set; }
            public int Size { get; set; }
            public int Vsize { get; set; }
            public int Weight { get; set; }
            public int Locktime { get; set; }
            public List<RawTransactionVin> Vin { get; set; }
            public List<RawTransactionVout> Vout { get; set; }
            public string Hex { get; set; }
            public string Blockhash { get; set; }
            public int Confirmations { get; set; }
            public long Time { get; set; }
            public long Blocktime { get; set; }
        }

        internal class RawTransactionVin
        {
            public string Txid { get; set; }
            public int Vout { get; set; }
            public RawTransactionScriptSig ScriptSig { get; set; }
            public List<string> Txinwitness { get; set; }
            public long Sequence { get; set; }
        }

        internal class RawTransactionScriptSig
        {
            public string Asm { get; set; }
            public string Hex { get; set; }
        }

        internal class RawTransactionVout
        {
            public decimal Value { get; set; }
            public int N { get; set; }
            public RawTransactionScriptPubKey ScriptPubKey { get; set; }
        }

        public class RawTransactionScriptPubKey
        {
            public string Asm { get; set; }
            public string Desc { get; set; }
            public string Hex { get; set; }
            public string Address { get; set; }
            public string Type { get; set; }
        }

        public class PaymentInvoice : Objeto
        {
            internal string Id { get; private set; }

            internal string PaymentId { get; private set; }

            internal decimal Due { get; private set; }
            internal decimal TotalPaid { get; private set; }
            internal decimal Rate { get; private set; }

            internal string InvoceStoreId { get; private set; }

            internal PaymentInvoice(string id, decimal invoiceDue, decimal totalPaid, decimal rate, string paymentId, string invoceStoreId)
            {
                if (string.IsNullOrWhiteSpace(id)) throw new ArgumentException("Invoice ID cannot be null or empty.", nameof(id));
                if (string.IsNullOrWhiteSpace(paymentId)) throw new ArgumentException("Payment ID cannot be null or empty.", nameof(paymentId));
                if (totalPaid < 0) throw new ArgumentOutOfRangeException(nameof(totalPaid), "Total paid cannot be negative.");
                if (rate < 0) throw new ArgumentOutOfRangeException(nameof(rate), "Rate cannot be negative.");
                if (string.IsNullOrEmpty(invoceStoreId)) throw new ArgumentException("Invoice store ID cannot be null or empty.", nameof(invoceStoreId));

                Id = id;
                Due = invoiceDue;
                TotalPaid = totalPaid;
                Rate = rate;
                PaymentId = paymentId;
                InvoceStoreId = invoceStoreId;
            }

            public enum DueStatus
            {
                Paid,
                Overpaid,
                Underpaid,
                NotSet
            }

            internal DueStatus Status
            {
                get
                {
                    if (Due == 0)
                    {
                        return DueStatus.Paid;
                    }
                    else if (Due < 0)
                    {
                        return DueStatus.Overpaid;
                    }
                    else if (Due > 0)
                    {
                        return DueStatus.Underpaid;
                    }
                    else
                    {
                        return DueStatus.NotSet;
                    }
                }
            }
        }
    }
}
