﻿using Puppeteer.EventSourcing.Libraries;
using System;
using System.Threading.Tasks;

namespace GamesEngine.Business.Liquidity.Sentinels
{
    internal abstract class SentinelTask : Objeto
    {
        protected Task task;
        internal Task Task => task;
        internal string Type => this.GetType().Name;
        public virtual string ToString() => $"{Type} Task";
        internal abstract void StartTask(DateTime startedAt);
    }
}
